use tauri::State;
use crate::types::*;
use crate::database::Database;
use crate::config::ConfigManager;
use std::sync::Mutex;

pub mod chat;
pub mod memory;
pub mod tools;
pub mod settings;

// 应用状态
pub struct AppState {
    pub database: Mutex<Database>,
    pub config_manager: Mutex<ConfigManager>,
}

impl AppState {
    pub async fn new(database_url: &str) -> AppResult<Self> {
        let database = Database::new(database_url).await?;
        let config_manager = ConfigManager::new(database.clone());
        
        Ok(AppState {
            database: Mutex::new(database),
            config_manager: Mutex::new(config_manager),
        })
    }
}

// 聊天相关命令
#[tauri::command]
pub async fn send_message(
    message: String,
    model: Option<String>,
    state: State<'_, AppState>,
) -> Result<ChatResponse, String> {
    chat::send_message(message, model, state).await
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn get_chat_history(
    limit: Option<usize>,
    state: State<'_, AppState>,
) -> Result<Vec<ChatMessage>, String> {
    chat::get_chat_history(limit, state).await
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn clear_chat_history(
    state: State<'_, AppState>,
) -> Result<(), String> {
    chat::clear_chat_history(state).await
        .map_err(|e| e.to_string())
}

// 记忆管理命令
#[tauri::command]
pub async fn search_memories(
    query: String,
    limit: Option<usize>,
    state: State<'_, AppState>,
) -> Result<Vec<Memory>, String> {
    memory::search_memories(query, limit, state).await
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn add_memory(
    content: String,
    importance: u8,
    state: State<'_, AppState>,
) -> Result<Memory, String> {
    memory::add_memory(content, importance, state).await
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn update_memory(
    id: String,
    content: String,
    importance: u8,
    state: State<'_, AppState>,
) -> Result<(), String> {
    memory::update_memory(id, content, importance, state).await
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn delete_memory(
    id: String,
    state: State<'_, AppState>,
) -> Result<(), String> {
    memory::delete_memory(id, state).await
        .map_err(|e| e.to_string())
}

// 工具相关命令
#[tauri::command]
pub async fn execute_tool(
    tool_name: String,
    params: serde_json::Value,
    state: State<'_, AppState>,
) -> Result<ToolResult, String> {
    tools::execute_tool(tool_name, params, state).await
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn get_available_tools(
    state: State<'_, AppState>,
) -> Result<Vec<ToolInfo>, String> {
    tools::get_available_tools(state).await
        .map_err(|e| e.to_string())
}

// 设置相关命令
#[tauri::command]
pub async fn get_settings(
    state: State<'_, AppState>,
) -> Result<AppSettings, String> {
    settings::get_settings(state).await
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn update_settings(
    settings: AppSettings,
    state: State<'_, AppState>,
) -> Result<(), String> {
    settings::update_settings(settings, state).await
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn reset_settings(
    state: State<'_, AppState>,
) -> Result<(), String> {
    settings::reset_settings(state).await
        .map_err(|e| e.to_string())
}

// 思源笔记相关命令
#[tauri::command]
pub async fn siyuan_create_note(
    title: String,
    content: String,
    notebook: Option<String>,
    state: State<'_, AppState>,
) -> Result<String, String> {
    use crate::tools::siyuan_notes::SiYuanNotesTool;
    use crate::tools::Tool;

    let tool = SiYuanNotesTool::new();
    let config_manager = state.config_manager.lock().unwrap().clone();

    let params = serde_json::json!({
        "action": "create",
        "title": title,
        "content": content,
        "notebook": notebook
    });

    tool.execute(params, &config_manager).await
        .map(|result| {
            // 从结果中提取文档ID
            if let Some(start) = result.find("ID：") {
                let id_part = &result[start + 3..];
                if let Some(end) = id_part.find('\n') {
                    id_part[..end].to_string()
                } else {
                    id_part.to_string()
                }
            } else {
                "unknown".to_string()
            }
        })
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn siyuan_search_notes(
    query: String,
    limit: Option<usize>,
    state: State<'_, AppState>,
) -> Result<Vec<SiYuanBlock>, String> {
    use crate::tools::siyuan_notes::SiYuanNotesTool;
    use crate::tools::Tool;

    let tool = SiYuanNotesTool::new();
    let config_manager = state.config_manager.lock().unwrap().clone();

    let params = serde_json::json!({
        "action": "search",
        "query": query,
        "limit": limit.unwrap_or(10)
    });

    // 这里简化处理，实际应该解析搜索结果
    tool.execute(params, &config_manager).await
        .map(|_| Vec::new()) // 暂时返回空列表
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn siyuan_get_note(
    doc_id: String,
    state: State<'_, AppState>,
) -> Result<SiYuanDocument, String> {
    use crate::tools::siyuan_notes::SiYuanNotesTool;
    use crate::tools::Tool;

    let tool = SiYuanNotesTool::new();
    let config_manager = state.config_manager.lock().unwrap().clone();

    let params = serde_json::json!({
        "action": "get",
        "doc_id": doc_id
    });

    tool.execute(params, &config_manager).await
        .map(|content| SiYuanDocument {
            id: doc_id.clone(),
            title: "Unknown".to_string(),
            content,
            path: "/".to_string(),
            notebook: "Unknown".to_string(),
            created: "".to_string(),
            updated: "".to_string(),
        })
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn siyuan_update_note(
    doc_id: String,
    content: String,
    state: State<'_, AppState>,
) -> Result<(), String> {
    use crate::tools::siyuan_notes::SiYuanNotesTool;
    use crate::tools::Tool;

    let tool = SiYuanNotesTool::new();
    let config_manager = state.config_manager.lock().unwrap().clone();

    let params = serde_json::json!({
        "action": "update",
        "doc_id": doc_id,
        "content": content
    });

    tool.execute(params, &config_manager).await
        .map(|_| ())
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn siyuan_delete_note(
    doc_id: String,
    state: State<'_, AppState>,
) -> Result<(), String> {
    use crate::tools::siyuan_notes::SiYuanNotesTool;
    use crate::tools::Tool;

    let tool = SiYuanNotesTool::new();
    let config_manager = state.config_manager.lock().unwrap().clone();

    let params = serde_json::json!({
        "action": "delete",
        "doc_id": doc_id
    });

    tool.execute(params, &config_manager).await
        .map(|_| ())
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn siyuan_test_connection(
    host: String,
    port: u16,
    token: String,
    state: State<'_, AppState>,
) -> Result<bool, String> {
    use crate::tools::siyuan_notes::SiYuanNotesTool;
    use crate::tools::Tool;

    // 临时更新配置进行测试
    let mut config_manager = state.config_manager.lock().unwrap().clone();
    let mut settings = config_manager.get_settings().await.map_err(|e| e.to_string())?;
    settings.siyuan_settings.host = host;
    settings.siyuan_settings.port = port;
    settings.siyuan_settings.token = token;

    let tool = SiYuanNotesTool::new();
    let params = serde_json::json!({
        "action": "test_connection"
    });

    tool.execute(params, &config_manager).await
        .map(|result| result.contains("连接成功"))
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn siyuan_get_notebooks(
    state: State<'_, AppState>,
) -> Result<Vec<SiYuanNotebook>, String> {
    use crate::tools::siyuan_notes::SiYuanNotesTool;
    use crate::tools::Tool;

    let tool = SiYuanNotesTool::new();
    let config_manager = state.config_manager.lock().unwrap().clone();

    let params = serde_json::json!({
        "action": "list_notebooks"
    });

    // 这里简化处理，实际应该解析笔记本列表
    tool.execute(params, &config_manager).await
        .map(|_| Vec::new()) // 暂时返回空列表
        .map_err(|e| e.to_string())
}

// 系统信息命令
#[tauri::command]
pub async fn get_system_info() -> Result<SystemInfo, String> {


    let os = std::env::consts::OS.to_string();
    let arch = std::env::consts::ARCH.to_string();

    // 获取系统版本
    let version = if cfg!(target_os = "linux") {
        // 尝试读取 /etc/os-release
        std::fs::read_to_string("/etc/os-release")
            .unwrap_or_else(|_| "Unknown Linux".to_string())
            .lines()
            .find(|line| line.starts_with("PRETTY_NAME="))
            .and_then(|line| line.split('=').nth(1))
            .map(|s| s.trim_matches('"').to_string())
            .unwrap_or_else(|| "Unknown Linux".to_string())
    } else if cfg!(target_os = "windows") {
        "Windows".to_string()
    } else if cfg!(target_os = "macos") {
        "macOS".to_string()
    } else {
        "Unknown".to_string()
    };

    // 获取内存信息
    let (memory_total, memory_available) = if cfg!(target_os = "linux") {
        let meminfo = std::fs::read_to_string("/proc/meminfo").unwrap_or_default();
        let total = meminfo
            .lines()
            .find(|line| line.starts_with("MemTotal:"))
            .and_then(|line| line.split_whitespace().nth(1))
            .and_then(|s| s.parse::<u64>().ok())
            .unwrap_or(0) * 1024; // 转换为字节

        let available = meminfo
            .lines()
            .find(|line| line.starts_with("MemAvailable:"))
            .and_then(|line| line.split_whitespace().nth(1))
            .and_then(|s| s.parse::<u64>().ok())
            .unwrap_or(0) * 1024; // 转换为字节

        (total, available)
    } else {
        (0, 0) // 其他系统暂时返回0
    };

    // 获取CPU核心数
    let cpu_count = num_cpus::get() as u32;

    // 获取系统运行时间
    let uptime = if cfg!(target_os = "linux") {
        std::fs::read_to_string("/proc/uptime")
            .ok()
            .and_then(|content| content.split_whitespace().next().map(|s| s.to_string()))
            .and_then(|s| s.parse::<f64>().ok())
            .map(|f| f as u64)
            .unwrap_or(0)
    } else {
        0 // 其他系统暂时返回0
    };

    Ok(SystemInfo {
        os,
        arch,
        version,
        memory_total,
        memory_available,
        cpu_count,
        uptime,
    })
}

// 权限管理命令
#[tauri::command]
pub async fn update_tool_permission(
    tool_name: String,
    enabled: bool,
    state: State<'_, AppState>,
) -> Result<(), String> {
    let mut config_manager = state.config_manager.lock().unwrap().clone();
    let mut settings = config_manager.get_settings().await.map_err(|e| e.to_string())?;

    settings.ai_settings.tool_permissions.insert(tool_name, enabled);

    config_manager.update_settings(settings).await.map_err(|e| e.to_string())?;
    Ok(())
}

#[tauri::command]
pub async fn update_api_keys(
    api_keys: std::collections::HashMap<String, String>,
    state: State<'_, AppState>,
) -> Result<(), String> {
    let mut config_manager = state.config_manager.lock().unwrap().clone();
    let mut settings = config_manager.get_settings().await.map_err(|e| e.to_string())?;

    settings.ai_settings.api_keys = api_keys;

    config_manager.update_settings(settings).await.map_err(|e| e.to_string())?;
    Ok(())
}

#[tauri::command]
pub async fn get_all_siyuan_configs(
    state: State<'_, AppState>,
) -> Result<Vec<(String, String, String, u16, bool)>, String> {
    let database = state.database.lock().unwrap().clone();
    database.get_all_siyuan_configs().await.map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn save_siyuan_config(
    name: String,
    host: String,
    port: u16,
    token: String,
    is_active: bool,
    state: State<'_, AppState>,
) -> Result<String, String> {
    let database = state.database.lock().unwrap().clone();
    database.save_siyuan_config(&name, &host, port, &token, is_active).await.map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn update_siyuan_config(
    id: String,
    name: String,
    host: String,
    port: u16,
    token: String,
    is_active: bool,
    state: State<'_, AppState>,
) -> Result<(), String> {
    let database = state.database.lock().unwrap().clone();
    database.update_siyuan_config(&id, &name, &host, port, &token, is_active).await.map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn delete_siyuan_config(
    id: String,
    state: State<'_, AppState>,
) -> Result<(), String> {
    let database = state.database.lock().unwrap().clone();
    database.delete_siyuan_config(&id).await.map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn check_updates() -> Result<UpdateInfo, String> {
    // TODO: 实现更新检查功能
    Ok(UpdateInfo {
        available: false,
        version: None,
        download_url: None,
        release_notes: None,
    })
}
