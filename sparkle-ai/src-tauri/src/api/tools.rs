use tauri::State;
use crate::types::*;
use crate::api::AppState;
use crate::tools::ToolManager;
use crate::security::SecurityManager;

pub async fn execute_tool(
    tool_name: String,
    params: serde_json::Value,
    state: State<'_, AppState>,
) -> AppResult<ToolResult> {
    // 获取配置管理器
    let config_manager = state.config_manager.lock().unwrap().clone();

    // 安全验证
    let mut security_manager = SecurityManager::new(config_manager.clone());
    security_manager.validate_tool_execution(&tool_name, &params).await?;

    // 记录安全事件
    security_manager.log_security_event(
        "TOOL_EXECUTION",
        &format!("Tool: {}, Params: {}", tool_name, params),
        true
    ).await;

    // 创建工具管理器
    let tool_manager = ToolManager::new();

    // 执行工具
    let tool_result = tool_manager.execute_tool(&tool_name, params.clone(), &config_manager).await?;

    // 记录工具使用
    {
        let database = state.database.lock().unwrap().clone();
        database.save_tool_usage(
            &tool_name,
            &serde_json::to_string(&params).unwrap_or_default(),
            &tool_result,
            None // conversation_id 暂时为空
        ).await?;
    }

    Ok(tool_result)
}

pub async fn get_available_tools(
    state: State<'_, AppState>,
) -> AppResult<Vec<ToolInfo>> {
    // 创建工具管理器
    let tool_manager = ToolManager::new();

    // 获取配置管理器和权限设置
    let mut config_manager = state.config_manager.lock().unwrap().clone();
    let ai_settings = config_manager.get_ai_settings().await?;

    // 获取工具列表并设置权限状态
    let mut tools = tool_manager.get_available_tools();

    // 更新每个工具的启用状态
    for tool in &mut tools {
        tool.enabled = ai_settings.tool_permissions.get(&tool.name).copied().unwrap_or(false);
    }

    Ok(tools)
}
