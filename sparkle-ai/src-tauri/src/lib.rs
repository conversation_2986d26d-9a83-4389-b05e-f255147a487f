use tauri::Manager;

mod types;
mod database;
mod config;
mod api;
mod tools;

use api::AppState;

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    env_logger::init();

    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .plugin(tauri_plugin_shell::init())
        .setup(|app| {
            // 获取应用数据目录
            let app_data_dir = app.path().app_data_dir().expect("Failed to get app data dir");
            std::fs::create_dir_all(&app_data_dir).expect("Failed to create app data dir");

            // 数据库文件路径
            let db_path = app_data_dir.join("sparkle-ai.db");
            let database_url = format!("sqlite:{}", db_path.to_string_lossy());

            // 初始化应用状态
            let app_state = tauri::async_runtime::block_on(async {
                AppState::new(&database_url).await
            }).expect("Failed to initialize app state");

            app.manage(app_state);

            Ok(())
        })
        .invoke_handler(tauri::generate_handler![
            // 聊天相关
            api::send_message,
            api::get_chat_history,
            api::clear_chat_history,
            // 记忆管理
            api::search_memories,
            api::add_memory,
            api::update_memory,
            api::delete_memory,
            // 工具相关
            api::execute_tool,
            api::get_available_tools,
            // 设置相关
            api::get_settings,
            api::update_settings,
            api::reset_settings,
            // 思源笔记
            api::siyuan_create_note,
            api::siyuan_search_notes,
            api::siyuan_get_note,
            api::siyuan_update_note,
            api::siyuan_delete_note,
            api::siyuan_test_connection,
            api::siyuan_get_notebooks,
            // 系统信息
            api::get_system_info,
            api::check_updates,
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
