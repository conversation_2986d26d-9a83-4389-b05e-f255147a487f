use std::process::Command;
use std::time::Duration;
use tokio::time::timeout;
use crate::types::*;
use crate::config::ConfigManager;
use super::{Tool, ToolValidator};

#[derive(Clone)]
pub struct SafeShellTool;

impl SafeShellTool {
    pub fn new() -> Self {
        Self
    }
}

impl Tool for SafeShellTool {
    fn name(&self) -> &str {
        "safe_shell"
    }

    fn description(&self) -> &str {
        "安全执行Shell命令，支持文件操作和系统信息查询"
    }

    fn parameters_schema(&self) -> serde_json::Value {
        serde_json::json!({
            "type": "object",
            "properties": {
                "command": {
                    "type": "string",
                    "description": "要执行的Shell命令"
                },
                "working_directory": {
                    "type": "string",
                    "description": "工作目录（可选）"
                },
                "timeout_seconds": {
                    "type": "integer",
                    "description": "超时时间（秒），默认30秒",
                    "default": 30,
                    "minimum": 1,
                    "maximum": 300
                }
            },
            "required": ["command"]
        })
    }

    async fn execute(&self, params: serde_json::Value, config: &ConfigManager) -> AppResult<String> {
        // 验证参数
        ToolValidator::validate_parameters(self.name(), &params, &self.parameters_schema())?;

        let command = params.get("command")
            .and_then(|v| v.as_str())
            .ok_or_else(|| AppError::InvalidInput("Missing command parameter".to_string()))?;

        let working_directory = params.get("working_directory")
            .and_then(|v| v.as_str());

        let timeout_seconds = params.get("timeout_seconds")
            .and_then(|v| v.as_u64())
            .unwrap_or(30);

        // 获取安全设置
        let security_settings = config.clone().get_security_settings().await?;

        // 验证命令安全性
        ToolValidator::validate_command_safety(
            command,
            &security_settings.command_whitelist,
            &security_settings.command_blacklist,
        )?;

        // 检查超时设置
        let max_timeout = security_settings.max_execution_time / 1000; // 转换为秒
        if timeout_seconds > max_timeout {
            return Err(AppError::InvalidInput(
                format!("Timeout exceeds maximum allowed time: {} seconds", max_timeout)
            ));
        }

        // 执行命令
        self.execute_command(command, working_directory, timeout_seconds).await
    }

    fn is_safe(&self) -> bool {
        true // 通过白名单和黑名单机制保证安全
    }

    fn requires_permission(&self) -> bool {
        true
    }
}

impl SafeShellTool {
    async fn execute_command(
        &self,
        command: &str,
        working_directory: Option<&str>,
        timeout_seconds: u64,
    ) -> AppResult<String> {
        let mut cmd = if cfg!(target_os = "windows") {
            let mut cmd = Command::new("cmd");
            cmd.args(["/C", command]);
            cmd
        } else {
            let mut cmd = Command::new("sh");
            cmd.args(["-c", command]);
            cmd
        };

        // 设置工作目录
        if let Some(dir) = working_directory {
            cmd.current_dir(dir);
        }

        // 执行命令并设置超时
        let output = timeout(
            Duration::from_secs(timeout_seconds),
            tokio::task::spawn_blocking(move || cmd.output())
        ).await
        .map_err(|_| AppError::ToolExecution("Command execution timeout".to_string()))?
        .map_err(|e| AppError::ToolExecution(format!("Failed to spawn command: {}", e)))?
        .map_err(|e| AppError::ToolExecution(format!("Command execution failed: {}", e)))?;

        // 处理输出
        let stdout = String::from_utf8_lossy(&output.stdout);
        let stderr = String::from_utf8_lossy(&output.stderr);

        if output.status.success() {
            let mut result = stdout.to_string();
            if !stderr.is_empty() {
                result.push_str("\n[警告信息]\n");
                result.push_str(&stderr);
            }
            
            // 限制输出长度
            if result.len() > 10000 {
                result.truncate(10000);
                result.push_str("\n... [输出被截断]");
            }
            
            Ok(result)
        } else {
            let error_msg = if stderr.is_empty() {
                format!("Command failed with exit code: {}", output.status.code().unwrap_or(-1))
            } else {
                stderr.to_string()
            };
            
            Err(AppError::ToolExecution(error_msg))
        }
    }

    // 预定义的安全命令集合
    pub fn get_safe_commands() -> Vec<String> {
        vec![
            // 文件和目录操作
            "ls".to_string(), "ll".to_string(), "dir".to_string(),
            "pwd".to_string(), "cd".to_string(),
            "cat".to_string(), "head".to_string(), "tail".to_string(),
            "less".to_string(), "more".to_string(),
            "find".to_string(), "locate".to_string(),
            "grep".to_string(), "egrep".to_string(), "fgrep".to_string(),
            "wc".to_string(), "sort".to_string(), "uniq".to_string(),
            "cut".to_string(), "awk".to_string(), "sed".to_string(),
            
            // 系统信息
            "whoami".to_string(), "id".to_string(),
            "date".to_string(), "uptime".to_string(),
            "uname".to_string(), "hostname".to_string(),
            "ps".to_string(), "top".to_string(), "htop".to_string(),
            "free".to_string(), "df".to_string(), "du".to_string(),
            "lscpu".to_string(), "lsmem".to_string(),
            "lsblk".to_string(), "lsusb".to_string(), "lspci".to_string(),
            
            // 网络信息
            "ping".to_string(), "wget".to_string(), "curl".to_string(),
            "netstat".to_string(), "ss".to_string(),
            "ip".to_string(), "ifconfig".to_string(),
            
            // 文本处理
            "echo".to_string(), "printf".to_string(),
            "tr".to_string(), "rev".to_string(),
            "base64".to_string(), "md5sum".to_string(), "sha256sum".to_string(),
            
            // 压缩和归档
            "tar".to_string(), "gzip".to_string(), "gunzip".to_string(),
            "zip".to_string(), "unzip".to_string(),
            
            // 其他实用工具
            "which".to_string(), "whereis".to_string(), "type".to_string(),
            "history".to_string(), "alias".to_string(),
            "env".to_string(), "printenv".to_string(),
            "sleep".to_string(), "timeout".to_string(),
        ]
    }

    // 危险命令黑名单
    pub fn get_dangerous_commands() -> Vec<String> {
        vec![
            // 文件删除和移动
            "rm".to_string(), "rmdir".to_string(),
            "mv".to_string(), "cp".to_string(),
            "shred".to_string(), "wipe".to_string(),
            
            // 权限修改
            "chmod".to_string(), "chown".to_string(), "chgrp".to_string(),
            "setfacl".to_string(), "getfacl".to_string(),
            
            // 用户和权限管理
            "sudo".to_string(), "su".to_string(),
            "passwd".to_string(), "chpasswd".to_string(),
            "useradd".to_string(), "userdel".to_string(), "usermod".to_string(),
            "groupadd".to_string(), "groupdel".to_string(), "groupmod".to_string(),
            
            // 系统服务管理
            "systemctl".to_string(), "service".to_string(),
            "init".to_string(), "telinit".to_string(),
            "shutdown".to_string(), "reboot".to_string(), "halt".to_string(),
            
            // 磁盘和文件系统操作
            "mount".to_string(), "umount".to_string(),
            "fdisk".to_string(), "parted".to_string(), "gparted".to_string(),
            "mkfs".to_string(), "fsck".to_string(),
            "dd".to_string(), "format".to_string(),
            
            // 网络配置
            "iptables".to_string(), "ufw".to_string(), "firewall-cmd".to_string(),
            "route".to_string(), "ip route".to_string(),
            
            // 包管理
            "apt".to_string(), "yum".to_string(), "dnf".to_string(),
            "pacman".to_string(), "zypper".to_string(),
            "pip".to_string(), "npm".to_string(), "cargo".to_string(),
            
            // 编译和安装
            "make".to_string(), "cmake".to_string(),
            "gcc".to_string(), "g++".to_string(), "clang".to_string(),
            "configure".to_string(), "autogen".to_string(),
            
            // 其他危险操作
            "crontab".to_string(), "at".to_string(),
            "nohup".to_string(), "screen".to_string(), "tmux".to_string(),
            "kill".to_string(), "killall".to_string(), "pkill".to_string(),
        ]
    }
}
