use std::time::Instant;
use crate::types::*;
use crate::config::ConfigManager;

pub mod safe_shell;
pub mod web_search;
pub mod gaode_map;
pub mod siyuan_notes;

// 工具枚举，用于替代 dyn trait
#[derive(Clone)]
pub enum ToolInstance {
    SafeShell(safe_shell::SafeShellTool),
    WebSearch(web_search::WebSearchTool),
    GaodeMap(gaode_map::GaodeMapTool),
    SiYuanNotes(siyuan_notes::SiYuanNotesTool),
}

pub struct ToolManager;

pub trait Tool {
    fn name(&self) -> &str;
    fn description(&self) -> &str;
    fn parameters_schema(&self) -> serde_json::Value;
    async fn execute(&self, params: serde_json::Value, config: &ConfigManager) -> AppResult<String>;
    fn is_safe(&self) -> bool { true }
    fn requires_permission(&self) -> bool { true }
}

impl ToolInstance {
    pub fn name(&self) -> &str {
        match self {
            ToolInstance::SafeShell(tool) => tool.name(),
            ToolInstance::WebSearch(tool) => tool.name(),
            ToolInstance::GaodeMap(tool) => tool.name(),
            ToolInstance::SiYuanNotes(tool) => tool.name(),
        }
    }

    pub fn description(&self) -> &str {
        match self {
            ToolInstance::SafeShell(tool) => tool.description(),
            ToolInstance::WebSearch(tool) => tool.description(),
            ToolInstance::GaodeMap(tool) => tool.description(),
            ToolInstance::SiYuanNotes(tool) => tool.description(),
        }
    }

    pub fn parameters_schema(&self) -> serde_json::Value {
        match self {
            ToolInstance::SafeShell(tool) => tool.parameters_schema(),
            ToolInstance::WebSearch(tool) => tool.parameters_schema(),
            ToolInstance::GaodeMap(tool) => tool.parameters_schema(),
            ToolInstance::SiYuanNotes(tool) => tool.parameters_schema(),
        }
    }

    pub async fn execute(&self, params: serde_json::Value, config: &ConfigManager) -> AppResult<String> {
        match self {
            ToolInstance::SafeShell(tool) => tool.execute(params, config).await,
            ToolInstance::WebSearch(tool) => tool.execute(params, config).await,
            ToolInstance::GaodeMap(tool) => tool.execute(params, config).await,
            ToolInstance::SiYuanNotes(tool) => tool.execute(params, config).await,
        }
    }

    pub fn is_safe(&self) -> bool {
        match self {
            ToolInstance::SafeShell(tool) => tool.is_safe(),
            ToolInstance::WebSearch(tool) => tool.is_safe(),
            ToolInstance::GaodeMap(tool) => tool.is_safe(),
            ToolInstance::SiYuanNotes(tool) => tool.is_safe(),
        }
    }

    pub fn requires_permission(&self) -> bool {
        match self {
            ToolInstance::SafeShell(tool) => tool.requires_permission(),
            ToolInstance::WebSearch(tool) => tool.requires_permission(),
            ToolInstance::GaodeMap(tool) => tool.requires_permission(),
            ToolInstance::SiYuanNotes(tool) => tool.requires_permission(),
        }
    }
}

impl ToolManager {
    pub fn new() -> Self {
        Self
    }

    pub fn get_tool(&self, tool_name: &str) -> Option<ToolInstance> {
        match tool_name {
            "safe_shell" => Some(ToolInstance::SafeShell(safe_shell::SafeShellTool::new())),
            "web_search" => Some(ToolInstance::WebSearch(web_search::WebSearchTool::new())),
            "gaode_map" => Some(ToolInstance::GaodeMap(gaode_map::GaodeMapTool::new())),
            "siyuan_notes" => Some(ToolInstance::SiYuanNotes(siyuan_notes::SiYuanNotesTool::new())),
            _ => None,
        }
    }

    pub async fn execute_tool(
        &self,
        tool_name: &str,
        params: serde_json::Value,
        config: &ConfigManager,
    ) -> AppResult<ToolResult> {
        let start_time = Instant::now();

        // 获取工具实例
        let tool = self.get_tool(tool_name)
            .ok_or_else(|| AppError::ToolExecution(format!("Unknown tool: {}", tool_name)))?;

        // 检查权限
        if tool.requires_permission() {
            let ai_settings = config.clone().get_ai_settings().await?;
            let enabled = ai_settings.tool_permissions.get(tool_name).copied().unwrap_or(false);
            if !enabled {
                return Err(AppError::PermissionDenied(format!("Tool {} is disabled", tool_name)));
            }
        }

        // 执行工具
        let result = match tool.execute(params, config).await {
            Ok(result) => ToolResult {
                tool_name: tool_name.to_string(),
                success: true,
                result,
                execution_time: start_time.elapsed().as_millis() as u64,
                error: None,
            },
            Err(error) => ToolResult {
                tool_name: tool_name.to_string(),
                success: false,
                result: String::new(),
                execution_time: start_time.elapsed().as_millis() as u64,
                error: Some(error.to_string()),
            },
        };

        Ok(result)
    }

    pub fn get_available_tools(&self) -> Vec<ToolInfo> {
        let tool_names = vec!["safe_shell", "web_search", "gaode_map", "siyuan_notes"];

        tool_names.into_iter().filter_map(|name| {
            self.get_tool(name).map(|tool| ToolInfo {
                name: name.to_string(),
                description: tool.description().to_string(),
                parameters: tool.parameters_schema(),
                enabled: false, // 需要在调用处根据配置设置
            })
        }).collect()
    }

    pub fn get_tool_info(&self, tool_name: &str) -> Option<ToolInfo> {
        self.get_tool(tool_name).map(|tool| ToolInfo {
            name: tool_name.to_string(),
            description: tool.description().to_string(),
            parameters: tool.parameters_schema(),
            enabled: false, // 需要在调用处根据配置设置
        })
    }

    pub fn list_tool_names(&self) -> Vec<String> {
        vec!["safe_shell".to_string(), "web_search".to_string(), "gaode_map".to_string(), "siyuan_notes".to_string()]
    }
}

impl Default for ToolManager {
    fn default() -> Self {
        Self::new()
    }
}

// 工具验证器
pub struct ToolValidator;

impl ToolValidator {
    pub fn validate_parameters(
        tool_name: &str,
        params: &serde_json::Value,
        schema: &serde_json::Value,
    ) -> AppResult<()> {
        // 简单的参数验证，可以后续扩展为更完整的JSON Schema验证
        if let Some(required) = schema.get("required").and_then(|r| r.as_array()) {
            for required_field in required {
                if let Some(field_name) = required_field.as_str() {
                    if !params.get(field_name).is_some() {
                        return Err(AppError::InvalidInput(
                            format!("Missing required parameter '{}' for tool '{}'", field_name, tool_name)
                        ));
                    }
                }
            }
        }
        Ok(())
    }

    pub fn validate_command_safety(command: &str, whitelist: &[String], blacklist: &[String]) -> AppResult<()> {
        let command_parts: Vec<&str> = command.split_whitespace().collect();
        if command_parts.is_empty() {
            return Err(AppError::InvalidInput("Empty command".to_string()));
        }

        let base_command = command_parts[0];

        // 检查黑名单
        for blocked in blacklist {
            if base_command == blocked || command.contains(blocked) {
                return Err(AppError::PermissionDenied(
                    format!("Command '{}' is blocked for security reasons", blocked)
                ));
            }
        }

        // 检查白名单（如果白名单不为空）
        if !whitelist.is_empty() {
            let allowed = whitelist.iter().any(|allowed_cmd| base_command == allowed_cmd);
            if !allowed {
                return Err(AppError::PermissionDenied(
                    format!("Command '{}' is not in the whitelist", base_command)
                ));
            }
        }

        // 检查危险模式
        let dangerous_patterns = [
            "rm -rf", "sudo rm", "format", "mkfs", "dd if=", ">/dev/", 
            "chmod 777", "chmod -R", "chown -R", "passwd", "su -", 
            "systemctl stop", "systemctl disable", "service stop"
        ];

        for pattern in &dangerous_patterns {
            if command.contains(pattern) {
                return Err(AppError::PermissionDenied(
                    format!("Command contains dangerous pattern: {}", pattern)
                ));
            }
        }

        Ok(())
    }
}
