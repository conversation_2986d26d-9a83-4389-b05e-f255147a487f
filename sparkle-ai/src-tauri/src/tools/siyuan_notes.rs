use reqwest;
use serde::{Deserialize, Serialize};
use serde_json::json;
use crate::types::*;
use crate::config::ConfigManager;
use super::{Tool, ToolValidator};

#[derive(Clone)]
pub struct SiYuanNotesTool {
    client: reqwest::Client,
}

#[derive(Debug, Serialize, Deserialize)]
struct SiYuanApiResponse<T> {
    code: i32,
    msg: String,
    data: T,
}

#[derive(Debug, Serialize, Deserialize)]
struct CreateDocResponse {
    id: String,
}

#[derive(Debug, Serialize, Deserialize)]
struct SearchResponse {
    blocks: Vec<SearchBlock>,
}

#[derive(Debug, Serialize, Deserialize)]
struct SearchBlock {
    id: String,
    content: String,
    r#type: String,
    path: String,
    #[serde(rename = "hPath")]
    h_path: String,
}

#[derive(Debug, Serialize, Deserialize)]
struct NotebookInfo {
    id: String,
    name: String,
    icon: String,
    sort: i32,
    closed: bool,
}

#[derive(Debug, Serialize, Deserialize)]
struct ExportResponse {
    content: String,
}

impl SiYuanNotesTool {
    pub fn new() -> Self {
        Self {
            client: reqwest::Client::builder()
                .user_agent("Sparkle-AI/1.0")
                .timeout(std::time::Duration::from_secs(30))
                .build()
                .unwrap_or_default(),
        }
    }

    async fn make_request<T: for<'de> Deserialize<'de>>(
        &self,
        endpoint: &str,
        data: serde_json::Value,
        host: &str,
        port: u16,
        token: &str,
    ) -> AppResult<T> {
        let url = format!("http://{}:{}/api{}", host, port, endpoint);
        
        let response = self.client
            .post(&url)
            .header("Authorization", format!("Token {}", token))
            .header("Content-Type", "application/json")
            .json(&data)
            .send()
            .await
            .map_err(|e| AppError::Http(e))?;

        if !response.status().is_success() {
            return Err(AppError::ToolExecution(
                format!("思源笔记API请求失败: HTTP {}", response.status())
            ));
        }

        let api_response: SiYuanApiResponse<T> = response.json().await
            .map_err(|e| AppError::Http(e))?;

        if api_response.code != 0 {
            return Err(AppError::ToolExecution(
                format!("思源笔记API错误: {}", api_response.msg)
            ));
        }

        Ok(api_response.data)
    }

    async fn create_note(
        &self,
        title: &str,
        content: &str,
        notebook: Option<&str>,
        host: &str,
        port: u16,
        token: &str,
    ) -> AppResult<String> {
        // 如果没有指定笔记本，获取默认笔记本
        let notebook_id = if let Some(nb) = notebook {
            nb.to_string()
        } else {
            let notebooks: Vec<NotebookInfo> = self.make_request(
                "/notebook/lsNotebooks",
                json!({}),
                host,
                port,
                token,
            ).await?;

            if notebooks.is_empty() {
                return Err(AppError::ToolExecution("没有可用的笔记本".to_string()));
            }

            notebooks[0].id.clone()
        };

        let data = json!({
            "notebook": notebook_id,
            "path": format!("/{}", title),
            "title": title,
            "md": content
        });

        let response: CreateDocResponse = self.make_request(
            "/filetree/createDocWithMd",
            data,
            host,
            port,
            token,
        ).await?;

        Ok(response.id)
    }

    async fn search_notes(
        &self,
        query: &str,
        limit: usize,
        host: &str,
        port: u16,
        token: &str,
    ) -> AppResult<Vec<SearchBlock>> {
        let data = json!({
            "query": query,
            "types": {
                "document": true,
                "heading": true,
                "paragraph": true,
                "mathBlock": true,
                "table": true,
                "codeBlock": true,
                "htmlBlock": true
            },
            "method": 0,
            "orderBy": 0,
            "groupBy": 0
        });

        let response: SearchResponse = self.make_request(
            "/search/searchBlock",
            data,
            host,
            port,
            token,
        ).await?;

        Ok(response.blocks.into_iter().take(limit).collect())
    }

    async fn get_note_content(
        &self,
        doc_id: &str,
        host: &str,
        port: u16,
        token: &str,
    ) -> AppResult<String> {
        let data = json!({
            "id": doc_id
        });

        let response: ExportResponse = self.make_request(
            "/export/exportMdContent",
            data,
            host,
            port,
            token,
        ).await?;

        Ok(response.content)
    }

    async fn update_note(
        &self,
        doc_id: &str,
        content: &str,
        host: &str,
        port: u16,
        token: &str,
    ) -> AppResult<()> {
        let data = json!({
            "dataType": "markdown",
            "data": content,
            "id": doc_id
        });

        let _: serde_json::Value = self.make_request(
            "/block/updateBlock",
            data,
            host,
            port,
            token,
        ).await?;

        Ok(())
    }

    async fn delete_note(
        &self,
        doc_id: &str,
        host: &str,
        port: u16,
        token: &str,
    ) -> AppResult<()> {
        let data = json!({
            "id": doc_id
        });

        let _: serde_json::Value = self.make_request(
            "/block/deleteBlock",
            data,
            host,
            port,
            token,
        ).await?;

        Ok(())
    }

    async fn get_notebooks(
        &self,
        host: &str,
        port: u16,
        token: &str,
    ) -> AppResult<Vec<NotebookInfo>> {
        self.make_request(
            "/notebook/lsNotebooks",
            json!({}),
            host,
            port,
            token,
        ).await
    }

    async fn test_connection(
        &self,
        host: &str,
        port: u16,
        token: &str,
    ) -> AppResult<bool> {
        match self.get_notebooks(host, port, token).await {
            Ok(_) => Ok(true),
            Err(_) => Ok(false),
        }
    }

    fn format_search_results(&self, blocks: &[SearchBlock], query: &str) -> String {
        let mut result = format!("🔍 思源笔记搜索结果：{}\n\n", query);

        if blocks.is_empty() {
            result.push_str("❌ 未找到相关笔记\n");
            return result;
        }

        for (index, block) in blocks.iter().enumerate() {
            result.push_str(&format!("{}. **{}**\n", index + 1, block.h_path));
            result.push_str(&format!("   📄 类型：{}\n", block.r#type));
            result.push_str(&format!("   📝 内容：{}\n", 
                if block.content.len() > 100 {
                    format!("{}...", &block.content[..100])
                } else {
                    block.content.clone()
                }
            ));
            result.push_str(&format!("   🔗 ID：{}\n\n", block.id));
        }

        result.push_str(&format!("📊 共找到 {} 个结果\n", blocks.len()));
        result
    }
}

impl Tool for SiYuanNotesTool {
    fn name(&self) -> &str {
        "siyuan_notes"
    }

    fn description(&self) -> &str {
        "思源笔记工具，支持创建、搜索、更新和删除笔记"
    }

    fn parameters_schema(&self) -> serde_json::Value {
        serde_json::json!({
            "type": "object",
            "properties": {
                "action": {
                    "type": "string",
                    "description": "操作类型",
                    "enum": ["create", "search", "get", "update", "delete", "list_notebooks", "test_connection"]
                },
                "title": {
                    "type": "string",
                    "description": "笔记标题（用于create操作）"
                },
                "content": {
                    "type": "string",
                    "description": "笔记内容（用于create和update操作）"
                },
                "query": {
                    "type": "string",
                    "description": "搜索关键词（用于search操作）"
                },
                "doc_id": {
                    "type": "string",
                    "description": "文档ID（用于get、update、delete操作）"
                },
                "notebook": {
                    "type": "string",
                    "description": "笔记本ID（可选，用于create操作）"
                },
                "limit": {
                    "type": "integer",
                    "description": "搜索结果数量限制",
                    "default": 10,
                    "minimum": 1,
                    "maximum": 50
                }
            },
            "required": ["action"]
        })
    }

    async fn execute(&self, params: serde_json::Value, config: &ConfigManager) -> AppResult<String> {
        // 验证参数
        ToolValidator::validate_parameters(self.name(), &params, &self.parameters_schema())?;

        let action = params.get("action")
            .and_then(|v| v.as_str())
            .ok_or_else(|| AppError::InvalidInput("Missing action parameter".to_string()))?;

        // 获取思源笔记配置
        let siyuan_settings = config.clone().get_siyuan_settings().await?;
        
        if siyuan_settings.token.is_empty() {
            return Err(AppError::Configuration("思源笔记Token未配置".to_string()));
        }

        let host = &siyuan_settings.host;
        let port = siyuan_settings.port;
        let token = &siyuan_settings.token;

        match action {
            "create" => {
                let title = params.get("title")
                    .and_then(|v| v.as_str())
                    .ok_or_else(|| AppError::InvalidInput("Missing title parameter".to_string()))?;
                
                let content = params.get("content")
                    .and_then(|v| v.as_str())
                    .ok_or_else(|| AppError::InvalidInput("Missing content parameter".to_string()))?;
                
                let notebook = params.get("notebook").and_then(|v| v.as_str());
                
                let doc_id = self.create_note(title, content, notebook, host, port, token).await?;
                Ok(format!("✅ 笔记创建成功\n📄 标题：{}\n🔗 ID：{}", title, doc_id))
            }
            
            "search" => {
                let query = params.get("query")
                    .and_then(|v| v.as_str())
                    .ok_or_else(|| AppError::InvalidInput("Missing query parameter".to_string()))?;
                
                let limit = params.get("limit")
                    .and_then(|v| v.as_u64())
                    .unwrap_or(10) as usize;
                
                let blocks = self.search_notes(query, limit, host, port, token).await?;
                Ok(self.format_search_results(&blocks, query))
            }
            
            "get" => {
                let doc_id = params.get("doc_id")
                    .and_then(|v| v.as_str())
                    .ok_or_else(|| AppError::InvalidInput("Missing doc_id parameter".to_string()))?;
                
                let content = self.get_note_content(doc_id, host, port, token).await?;
                Ok(format!("📄 笔记内容：\n\n{}", content))
            }
            
            "update" => {
                let doc_id = params.get("doc_id")
                    .and_then(|v| v.as_str())
                    .ok_or_else(|| AppError::InvalidInput("Missing doc_id parameter".to_string()))?;
                
                let content = params.get("content")
                    .and_then(|v| v.as_str())
                    .ok_or_else(|| AppError::InvalidInput("Missing content parameter".to_string()))?;
                
                self.update_note(doc_id, content, host, port, token).await?;
                Ok(format!("✅ 笔记更新成功\n🔗 ID：{}", doc_id))
            }
            
            "delete" => {
                let doc_id = params.get("doc_id")
                    .and_then(|v| v.as_str())
                    .ok_or_else(|| AppError::InvalidInput("Missing doc_id parameter".to_string()))?;
                
                self.delete_note(doc_id, host, port, token).await?;
                Ok(format!("✅ 笔记删除成功\n🔗 ID：{}", doc_id))
            }
            
            "list_notebooks" => {
                let notebooks = self.get_notebooks(host, port, token).await?;
                let mut result = "📚 可用笔记本列表：\n\n".to_string();
                
                for notebook in notebooks {
                    result.push_str(&format!("• **{}** ({})\n", notebook.name, notebook.id));
                    result.push_str(&format!("  图标：{} 排序：{}\n\n", notebook.icon, notebook.sort));
                }
                
                Ok(result)
            }
            
            "test_connection" => {
                let connected = self.test_connection(host, port, token).await?;
                if connected {
                    Ok("✅ 思源笔记连接成功".to_string())
                } else {
                    Ok("❌ 思源笔记连接失败，请检查配置".to_string())
                }
            }
            
            _ => {
                Err(AppError::InvalidInput(format!("Unknown action: {}", action)))
            }
        }
    }

    fn is_safe(&self) -> bool {
        true
    }

    fn requires_permission(&self) -> bool {
        true
    }
}
