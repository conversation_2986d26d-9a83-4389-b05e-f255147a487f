use reqwest;
use serde::{Deserialize, Serialize};
use crate::types::*;
use crate::config::ConfigManager;
use super::{Tool, ToolValidator};

#[derive(Clone)]
pub struct GaodeMapTool {
    client: reqwest::Client,
}

#[derive(Debug, Serialize, Deserialize)]
struct GaodeResponse<T> {
    status: String,
    count: Option<String>,
    info: String,
    infocode: String,
    #[serde(flatten)]
    data: T,
}

#[derive(Debug, Serialize, Deserialize)]
struct WeatherData {
    lives: Vec<WeatherLive>,
    forecasts: Vec<WeatherForecast>,
}

#[derive(Debug, Serialize, Deserialize)]
struct WeatherLive {
    province: String,
    city: String,
    adcode: String,
    weather: String,
    temperature: String,
    winddirection: String,
    windpower: String,
    humidity: String,
    reporttime: String,
}

#[derive(Debug, Serialize, Deserialize)]
struct WeatherForecast {
    city: String,
    adcode: String,
    province: String,
    reporttime: String,
    casts: Vec<WeatherCast>,
}

#[derive(Debug, Serialize, Deserialize)]
struct WeatherCast {
    date: String,
    week: String,
    dayweather: String,
    nightweather: String,
    daytemp: String,
    nighttemp: String,
    daywind: String,
    nightwind: String,
    daypower: String,
    nightpower: String,
}

#[derive(Debug, Serialize, Deserialize)]
struct LocationData {
    geocodes: Vec<Geocode>,
}

#[derive(Debug, Serialize, Deserialize)]
struct Geocode {
    formatted_address: String,
    country: String,
    province: String,
    citycode: String,
    city: String,
    district: String,
    township: Vec<String>,
    neighborhood: serde_json::Value,
    building: serde_json::Value,
    adcode: String,
    street: Vec<String>,
    number: Vec<String>,
    location: String,
    level: String,
}

impl GaodeMapTool {
    pub fn new() -> Self {
        Self {
            client: reqwest::Client::builder()
                .user_agent("Sparkle-AI/1.0")
                .timeout(std::time::Duration::from_secs(30))
                .build()
                .unwrap_or_default(),
        }
    }

    async fn get_weather(&self, city: &str, api_key: &str) -> AppResult<String> {
        // 实时天气
        let live_url = "https://restapi.amap.com/v3/weather/weatherInfo";
        let live_response = self.client
            .get(live_url)
            .query(&[
                ("key", api_key),
                ("city", city),
                ("extensions", "base"),
            ])
            .send()
            .await
            .map_err(|e| AppError::Http(e))?;

        let live_data: GaodeResponse<WeatherData> = live_response.json().await
            .map_err(|e| AppError::Http(e))?;

        if live_data.status != "1" {
            return Err(AppError::ToolExecution(format!("高德地图API错误: {}", live_data.info)));
        }

        // 天气预报
        let forecast_url = "https://restapi.amap.com/v3/weather/weatherInfo";
        let forecast_response = self.client
            .get(forecast_url)
            .query(&[
                ("key", api_key),
                ("city", city),
                ("extensions", "all"),
            ])
            .send()
            .await
            .map_err(|e| AppError::Http(e))?;

        let forecast_data: GaodeResponse<WeatherData> = forecast_response.json().await
            .map_err(|e| AppError::Http(e))?;

        self.format_weather_info(&live_data.data, &forecast_data.data)
    }

    async fn get_location(&self, address: &str, api_key: &str) -> AppResult<String> {
        let url = "https://restapi.amap.com/v3/geocode/geo";
        
        let response = self.client
            .get(url)
            .query(&[
                ("key", api_key),
                ("address", address),
                ("output", "json"),
            ])
            .send()
            .await
            .map_err(|e| AppError::Http(e))?;

        let data: GaodeResponse<LocationData> = response.json().await
            .map_err(|e| AppError::Http(e))?;

        if data.status != "1" {
            return Err(AppError::ToolExecution(format!("高德地图API错误: {}", data.info)));
        }

        self.format_location_info(&data.data.geocodes, address)
    }

    async fn search_nearby(&self, location: &str, keyword: &str, api_key: &str) -> AppResult<String> {
        // 首先获取位置坐标
        let geocode_url = "https://restapi.amap.com/v3/geocode/geo";
        let geocode_response = self.client
            .get(geocode_url)
            .query(&[
                ("key", api_key),
                ("address", location),
            ])
            .send()
            .await
            .map_err(|e| AppError::Http(e))?;

        let geocode_data: GaodeResponse<LocationData> = geocode_response.json().await
            .map_err(|e| AppError::Http(e))?;

        if geocode_data.status != "1" || geocode_data.data.geocodes.is_empty() {
            return Err(AppError::ToolExecution("无法找到指定位置".to_string()));
        }

        let coordinates = &geocode_data.data.geocodes[0].location;

        // 搜索周边
        let search_url = "https://restapi.amap.com/v3/place/around";
        let search_response = self.client
            .get(search_url)
            .query(&[
                ("key", api_key),
                ("location", coordinates),
                ("keywords", keyword),
                ("radius", "3000"), // 3公里范围
                ("offset", "10"),   // 返回10个结果
            ])
            .send()
            .await
            .map_err(|e| AppError::Http(e))?;

        let search_text = search_response.text().await.map_err(|e| AppError::Http(e))?;
        
        // 简单解析搜索结果
        Ok(format!("🗺️ 在 {} 附近搜索 \"{}\" 的结果：\n\n{}", location, keyword, 
            if search_text.contains("\"pois\":[]") {
                "未找到相关地点".to_string()
            } else {
                "找到相关地点，详细信息请查看高德地图应用".to_string()
            }
        ))
    }

    fn format_weather_info(&self, live: &WeatherData, forecast: &WeatherData) -> AppResult<String> {
        let mut result = String::new();

        // 实时天气
        if let Some(live_weather) = live.lives.first() {
            result.push_str(&format!("🌤️ {} {} 实时天气\n\n", live_weather.province, live_weather.city));
            result.push_str(&format!("天气：{}\n", live_weather.weather));
            result.push_str(&format!("温度：{}°C\n", live_weather.temperature));
            result.push_str(&format!("风向：{}\n", live_weather.winddirection));
            result.push_str(&format!("风力：{}级\n", live_weather.windpower));
            result.push_str(&format!("湿度：{}%\n", live_weather.humidity));
            result.push_str(&format!("更新时间：{}\n\n", live_weather.reporttime));
        }

        // 天气预报
        if let Some(forecast_weather) = forecast.forecasts.first() {
            result.push_str("📅 未来几天天气预报：\n\n");
            
            for (index, cast) in forecast_weather.casts.iter().take(4).enumerate() {
                let day_name = match index {
                    0 => "今天",
                    1 => "明天",
                    2 => "后天",
                    _ => &cast.week,
                };
                
                result.push_str(&format!("**{}** ({})\n", day_name, cast.date));
                result.push_str(&format!("  白天：{} {}°C {}{}\n", 
                    cast.dayweather, cast.daytemp, cast.daywind, cast.daypower));
                result.push_str(&format!("  夜间：{} {}°C {}{}\n\n", 
                    cast.nightweather, cast.nighttemp, cast.nightwind, cast.nightpower));
            }
        }

        Ok(result)
    }

    fn format_location_info(&self, geocodes: &[Geocode], query: &str) -> AppResult<String> {
        if geocodes.is_empty() {
            return Ok(format!("❌ 未找到 \"{}\" 的位置信息", query));
        }

        let mut result = format!("📍 \"{}\" 的位置信息：\n\n", query);

        for (index, geocode) in geocodes.iter().take(3).enumerate() {
            result.push_str(&format!("{}. **{}**\n", index + 1, geocode.formatted_address));
            result.push_str(&format!("   省份：{}\n", geocode.province));
            result.push_str(&format!("   城市：{}\n", geocode.city));
            result.push_str(&format!("   区县：{}\n", geocode.district));
            result.push_str(&format!("   坐标：{}\n", geocode.location));
            result.push_str(&format!("   行政区代码：{}\n\n", geocode.adcode));
        }

        Ok(result)
    }
}

impl Tool for GaodeMapTool {
    fn name(&self) -> &str {
        "gaode_map"
    }

    fn description(&self) -> &str {
        "高德地图工具，支持天气查询、位置搜索和周边查询"
    }

    fn parameters_schema(&self) -> serde_json::Value {
        serde_json::json!({
            "type": "object",
            "properties": {
                "action": {
                    "type": "string",
                    "description": "操作类型",
                    "enum": ["weather", "location", "nearby"],
                    "default": "weather"
                },
                "location": {
                    "type": "string",
                    "description": "位置名称（城市名或具体地址）"
                },
                "keyword": {
                    "type": "string",
                    "description": "搜索关键词（用于nearby操作）"
                }
            },
            "required": ["location"]
        })
    }

    async fn execute(&self, params: serde_json::Value, config: &ConfigManager) -> AppResult<String> {
        // 验证参数
        ToolValidator::validate_parameters(self.name(), &params, &self.parameters_schema())?;

        let action = params.get("action")
            .and_then(|v| v.as_str())
            .unwrap_or("weather");

        let location = params.get("location")
            .and_then(|v| v.as_str())
            .ok_or_else(|| AppError::InvalidInput("Missing location parameter".to_string()))?;

        // 获取高德地图API密钥
        let ai_settings = config.clone().get_ai_settings().await?;
        let api_key = ai_settings.api_keys.get("gaode_map")
            .ok_or_else(|| AppError::Configuration("高德地图API密钥未配置".to_string()))?;

        match action {
            "weather" => {
                self.get_weather(location, api_key).await
            }
            "location" => {
                self.get_location(location, api_key).await
            }
            "nearby" => {
                let keyword = params.get("keyword")
                    .and_then(|v| v.as_str())
                    .ok_or_else(|| AppError::InvalidInput("Missing keyword parameter for nearby search".to_string()))?;
                
                self.search_nearby(location, keyword, api_key).await
            }
            _ => {
                Err(AppError::InvalidInput(format!("Unknown action: {}", action)))
            }
        }
    }

    fn is_safe(&self) -> bool {
        true
    }

    fn requires_permission(&self) -> bool {
        true
    }
}
