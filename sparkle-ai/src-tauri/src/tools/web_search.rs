use reqwest;
use serde::{Deserialize, Serialize};
use crate::types::*;
use crate::config::ConfigManager;
use super::{<PERSON><PERSON>, <PERSON><PERSON>Validator};

#[derive(Clone)]
pub struct WebSearchTool {
    client: reqwest::Client,
}

#[derive(Debug, Serialize, Deserialize)]
struct SearchResult {
    title: String,
    url: String,
    snippet: String,
}

#[derive(Debug, Serialize, Deserialize)]
struct SearchResponse {
    results: Vec<SearchResult>,
    total_results: u64,
    search_time: f64,
}

impl WebSearchTool {
    pub fn new() -> Self {
        Self {
            client: reqwest::Client::builder()
                .user_agent("Sparkle-AI/1.0")
                .timeout(std::time::Duration::from_secs(30))
                .build()
                .unwrap_or_default(),
        }
    }

    async fn search_duckduckgo(&self, query: &str, limit: usize) -> AppResult<Vec<SearchResult>> {
        // 使用 DuckDuckGo 的即时答案 API
        let url = format!(
            "https://api.duckduckgo.com/?q={}&format=json&no_html=1&skip_disambig=1",
            urlencoding::encode(query)
        );

        let response = self.client
            .get(&url)
            .send()
            .await
            .map_err(|e| AppError::Http(e))?;

        let text = response.text().await.map_err(|e| AppError::Http(e))?;
        
        // 解析 DuckDuckGo 响应
        let json: serde_json::Value = serde_json::from_str(&text)
            .map_err(|e| AppError::Serialization(e))?;

        let mut results = Vec::new();

        // 处理即时答案
        if let Some(abstract_text) = json.get("Abstract").and_then(|v| v.as_str()) {
            if !abstract_text.is_empty() {
                results.push(SearchResult {
                    title: json.get("Heading").and_then(|v| v.as_str()).unwrap_or("DuckDuckGo 即时答案").to_string(),
                    url: json.get("AbstractURL").and_then(|v| v.as_str()).unwrap_or("").to_string(),
                    snippet: abstract_text.to_string(),
                });
            }
        }

        // 处理相关主题
        if let Some(related_topics) = json.get("RelatedTopics").and_then(|v| v.as_array()) {
            for topic in related_topics.iter().take(limit.saturating_sub(results.len())) {
                if let (Some(text), Some(url)) = (
                    topic.get("Text").and_then(|v| v.as_str()),
                    topic.get("FirstURL").and_then(|v| v.as_str())
                ) {
                    results.push(SearchResult {
                        title: "相关主题".to_string(),
                        url: url.to_string(),
                        snippet: text.to_string(),
                    });
                }
            }
        }

        // 如果没有结果，返回一个说明
        if results.is_empty() {
            results.push(SearchResult {
                title: "搜索结果".to_string(),
                url: String::new(),
                snippet: format!("未找到关于 '{}' 的详细信息，建议尝试更具体的搜索词。", query),
            });
        }

        Ok(results)
    }

    async fn search_bing(&self, query: &str, limit: usize, api_key: &str) -> AppResult<Vec<SearchResult>> {
        let url = "https://api.bing.microsoft.com/v7.0/search";
        
        let response = self.client
            .get(url)
            .header("Ocp-Apim-Subscription-Key", api_key)
            .query(&[
                ("q", query),
                ("count", &limit.to_string()),
                ("mkt", "zh-CN"),
                ("safesearch", "Moderate"),
            ])
            .send()
            .await
            .map_err(|e| AppError::Http(e))?;

        if !response.status().is_success() {
            return Err(AppError::Http(reqwest::Error::from(response.error_for_status().unwrap_err())));
        }

        let json: serde_json::Value = response.json().await.map_err(|e| AppError::Http(e))?;
        
        let mut results = Vec::new();
        
        if let Some(web_pages) = json.get("webPages").and_then(|wp| wp.get("value")).and_then(|v| v.as_array()) {
            for page in web_pages {
                if let (Some(name), Some(url), Some(snippet)) = (
                    page.get("name").and_then(|v| v.as_str()),
                    page.get("url").and_then(|v| v.as_str()),
                    page.get("snippet").and_then(|v| v.as_str())
                ) {
                    results.push(SearchResult {
                        title: name.to_string(),
                        url: url.to_string(),
                        snippet: snippet.to_string(),
                    });
                }
            }
        }

        Ok(results)
    }

    fn format_search_results(&self, results: &[SearchResult], query: &str) -> String {
        let mut output = format!("🔍 搜索结果：{}\n\n", query);
        
        for (index, result) in results.iter().enumerate() {
            output.push_str(&format!(
                "{}. **{}**\n",
                index + 1,
                result.title
            ));
            
            if !result.url.is_empty() {
                output.push_str(&format!("   🔗 {}\n", result.url));
            }
            
            output.push_str(&format!("   📝 {}\n\n", result.snippet));
        }

        if results.is_empty() {
            output.push_str("❌ 未找到相关结果，请尝试其他搜索词。\n");
        } else {
            output.push_str(&format!("📊 共找到 {} 个结果\n", results.len()));
        }

        output
    }
}

impl Tool for WebSearchTool {
    fn name(&self) -> &str {
        "web_search"
    }

    fn description(&self) -> &str {
        "网络搜索工具，支持多个搜索引擎"
    }

    fn parameters_schema(&self) -> serde_json::Value {
        serde_json::json!({
            "type": "object",
            "properties": {
                "query": {
                    "type": "string",
                    "description": "搜索关键词或问题"
                },
                "limit": {
                    "type": "integer",
                    "description": "返回结果数量限制",
                    "default": 5,
                    "minimum": 1,
                    "maximum": 20
                },
                "engine": {
                    "type": "string",
                    "description": "搜索引擎选择：duckduckgo, bing",
                    "default": "duckduckgo",
                    "enum": ["duckduckgo", "bing"]
                }
            },
            "required": ["query"]
        })
    }

    async fn execute(&self, params: serde_json::Value, config: &ConfigManager) -> AppResult<String> {
        // 验证参数
        ToolValidator::validate_parameters(self.name(), &params, &self.parameters_schema())?;

        let query = params.get("query")
            .and_then(|v| v.as_str())
            .ok_or_else(|| AppError::InvalidInput("Missing query parameter".to_string()))?;

        let limit = params.get("limit")
            .and_then(|v| v.as_u64())
            .unwrap_or(5) as usize;

        let engine = params.get("engine")
            .and_then(|v| v.as_str())
            .unwrap_or("duckduckgo");

        // 验证搜索词
        if query.trim().is_empty() {
            return Err(AppError::InvalidInput("Search query cannot be empty".to_string()));
        }

        if query.len() > 200 {
            return Err(AppError::InvalidInput("Search query too long (max 200 characters)".to_string()));
        }

        // 执行搜索
        let results = match engine {
            "bing" => {
                // 获取 Bing API 密钥
                let ai_settings = config.clone().get_ai_settings().await?;
                if let Some(api_key) = ai_settings.api_keys.get("bing_search") {
                    self.search_bing(query, limit, api_key).await?
                } else {
                    // 如果没有 Bing API 密钥，回退到 DuckDuckGo
                    self.search_duckduckgo(query, limit).await?
                }
            }
            _ => {
                // 默认使用 DuckDuckGo
                self.search_duckduckgo(query, limit).await?
            }
        };

        Ok(self.format_search_results(&results, query))
    }

    fn is_safe(&self) -> bool {
        true
    }

    fn requires_permission(&self) -> bool {
        true
    }
}
