# Sparkle-AI

一个现代化的智能系统助手，基于 Tauri 构建的跨平台桌面应用。

## ✨ 功能特性

- 🤖 **AI 对话**: 支持多种 AI 模型，包括 DeepSeek、通义千问等
- 🧠 **记忆管理**: 智能记忆系统，AI 可以记住重要信息
- 🛠️ **工具集成**: 集成多种实用工具，包括文件操作、网络搜索等
- 📝 **思源笔记**: 与思源笔记深度集成，支持笔记创建和管理
- ⚙️ **个性化设置**: 丰富的设置选项，支持主题切换、快捷键配置
- 🔒 **安全可靠**: 本地运行，数据安全，支持命令白名单和沙箱模式

## 🛠️ 技术栈

### 前端
- **React 18** - 现代化前端框架
- **TypeScript** - 类型安全的 JavaScript
- **Tailwind CSS** - 实用优先的 CSS 框架
- **Zustand** - 轻量级状态管理
- **React Router** - 客户端路由

### 后端
- **Tauri** - 跨平台桌面应用框架
- **Rust** - 高性能后端语言
- **SQLite** - 轻量级数据库

## 🚀 快速开始

### 环境要求
- Node.js 18+
- Rust 1.70+

### 开发模式
```bash
# 安装依赖
cnpm install

# 启动开发服务器 (端口 8080)
npm run dev

# 启动 Tauri 开发模式
npm run tauri dev
```

## 🎯 开发状态

### ✅ 已完成
- [x] 基础框架搭建
- [x] UI 组件库开发
- [x] 状态管理系统
- [x] 聊天界面
- [x] 设置页面
- [x] 记忆管理页面

### 🚧 开发中
- [ ] 后端 API 集成
- [ ] AI 模型集成
- [ ] 工具系统实现

## 📝 使用说明

1. 访问 http://localhost:8080 查看应用
2. 在聊天界面与 AI 对话（当前为模拟模式）
3. 在设置页面配置应用参数
4. 在记忆页面管理 AI 记忆

## Recommended IDE Setup

- [VS Code](https://code.visualstudio.com/) + [Tauri](https://marketplace.visualstudio.com/items?itemName=tauri-apps.tauri-vscode) + [rust-analyzer](https://marketplace.visualstudio.com/items?itemName=rust-lang.rust-analyzer)
