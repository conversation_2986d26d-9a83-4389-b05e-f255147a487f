#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="$basedir/../../../../../sucrase@3.35.0/node_modules"
else
  export NODE_PATH="$NODE_PATH:$basedir/../../../../../sucrase@3.35.0/node_modules"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../sucrase@3.35.0/node_modules/sucrase/bin/sucrase" "$@"
else
  exec node  "$basedir/../../../../../sucrase@3.35.0/node_modules/sucrase/bin/sucrase" "$@"
fi
