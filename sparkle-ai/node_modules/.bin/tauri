#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="$basedir/../.store/@tauri-apps+cli@2.6.2/node_modules/@tauri-apps"
else
  export NODE_PATH="$NODE_PATH:$basedir/../.store/@tauri-apps+cli@2.6.2/node_modules/@tauri-apps"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../.store/@tauri-apps+cli@2.6.2/node_modules/@tauri-apps/cli/tauri.js" "$@"
else
  exec node  "$basedir/../.store/@tauri-apps+cli@2.6.2/node_modules/@tauri-apps/cli/tauri.js" "$@"
fi
