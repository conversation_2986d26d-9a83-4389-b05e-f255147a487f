{"@tauri-apps/api": ["2.6.0"], "@tauri-apps/plugin-shell": ["2.3.0"], "postcss": ["8.5.6"], "react": ["18.3.1"], "@types/react": ["18.3.23"], "autoprefixer": ["10.4.21"], "postcss-value-parser": ["4.2.0"], "@types/node": ["24.0.14"], "picocolors": ["1.1.1"], "react-router-dom": ["7.6.3"], "source-map-js": ["1.2.1"], "nanoid": ["3.3.11"], "normalize-range": ["0.1.2"], "zustand": ["5.0.6"], "@types/prop-types": ["15.7.15"], "csstype": ["3.1.3"], "fraction.js": ["4.3.7"], "loose-envify": ["1.4.0"], "@tauri-apps/plugin-opener": ["2.4.0"], "@types/react-dom": ["18.3.7"], "@babel/core": ["7.28.0"], "vite": ["6.3.5"], "undici-types": ["7.8.0"], "picomatch": ["4.0.2", "2.3.1"], "browserslist": ["4.25.1"], "react-dom": ["18.3.1"], "js-tokens": ["4.0.0"], "@babel/code-frame": ["7.27.1"], "react-router": ["7.6.3"], "@ampproject/remapping": ["2.3.0"], "convert-source-map": ["2.0.0"], "tinyglobby": ["0.2.14"], "semver": ["6.3.1"], "@vitejs/plugin-react": ["4.6.0"], "debug": ["4.4.1"], "@babel/helper-compilation-targets": ["7.27.2"], "@babel/helper-module-transforms": ["7.27.3"], "@babel/generator": ["7.28.0"], "node-releases": ["2.0.19"], "gensync": ["1.0.0-beta.2"], "@babel/traverse": ["7.28.0"], "fdir": ["6.4.6"], "tailwindcss": ["3.4.17"], "@babel/template": ["7.27.2"], "set-cookie-parser": ["2.7.1"], "@babel/helper-validator-identifier": ["7.27.1"], "cookie": ["1.0.2"], "esbuild": ["0.25.6"], "normalize-path": ["3.0.0"], "glob-parent": ["6.0.2", "5.1.2"], "arg": ["5.0.2"], "is-glob": ["4.0.3"], "didyoumean": ["1.2.2"], "postcss-nested": ["6.2.0"], "lilconfig": ["3.1.3"], "micromatch": ["4.0.8"], "update-browserslist-db": ["1.1.3"], "dlv": ["1.1.3"], "postcss-js": ["4.0.1"], "postcss-load-config": ["4.0.2"], "object-hash": ["3.0.0"], "json5": ["2.2.3"], "@babel/helper-validator-option": ["7.27.1"], "postcss-import": ["15.1.0"], "@babel/parser": ["7.28.0"], "chokidar": ["3.6.0"], "jsesc": ["3.1.0"], "electron-to-chromium": ["1.5.183"], "@tauri-apps/cli": ["2.6.2"], "scheduler": ["0.23.2"], "lru-cache": ["5.1.1", "10.4.3"], "ms": ["2.1.3"], "@jridgewell/trace-mapping": ["0.3.29"], "@babel/helper-globals": ["7.28.0"], "is-extglob": ["2.1.1"], "camelcase-css": ["2.0.1"], "@babel/compat-data": ["7.28.0"], "@alloc/quick-lru": ["5.2.0"], "read-cache": ["1.0.0"], "@babel/helper-module-imports": ["7.27.1"], "postcss-selector-parser": ["6.1.2"], "braces": ["3.0.3"], "is-binary-path": ["2.1.0"], "anymatch": ["3.1.3"], "readdirp": ["3.6.0"], "@jridgewell/gen-mapping": ["0.3.12"], "fast-glob": ["3.3.3"], "pify": ["2.3.0"], "@babel/helpers": ["7.27.6"], "util-deprecate": ["1.0.2"], "fill-range": ["7.1.1"], "escalade": ["3.2.0"], "cssesc": ["3.0.0"], "binary-extensions": ["2.3.0"], "merge2": ["1.4.1"], "@nodelib/fs.stat": ["2.0.5"], "to-regex-range": ["5.0.1"], "@nodelib/fs.walk": ["1.2.8"], "@rolldown/pluginutils": ["1.0.0-beta.19"], "yallist": ["3.1.1"], "rollup": ["4.45.1"], "jiti": ["1.21.7"], "is-number": ["7.0.0"], "resolve": ["1.22.10"], "@babel/plugin-transform-react-jsx-source": ["7.27.1"], "@types/babel__core": ["7.20.5"], "@jridgewell/resolve-uri": ["3.1.2"], "fastq": ["1.19.1"], "@nodelib/fs.scandir": ["2.1.5"], "@jridgewell/sourcemap-codec": ["1.5.4"], "path-parse": ["1.0.7"], "react-refresh": ["0.17.0"], "supports-preserve-symlinks-flag": ["1.0.0"], "is-core-module": ["2.16.1"], "run-parallel": ["1.2.0"], "reusify": ["1.1.0"], "@babel/types": ["7.28.1"], "hasown": ["2.0.2"], "queue-microtask": ["1.2.3"], "sucrase": ["3.35.0"], "@types/estree": ["1.0.8"], "function-bind": ["1.1.2"], "lines-and-columns": ["1.2.4"], "pirates": ["4.0.7"], "mz": ["2.7.0"], "commander": ["4.1.1"], "ts-interface-checker": ["0.1.13"], "caniuse-lite": ["1.0.30001727"], "yaml": ["2.8.0"], "object-assign": ["4.1.1"], "thenify-all": ["1.6.0"], "@babel/plugin-transform-react-jsx-self": ["7.27.1"], "any-promise": ["1.3.0"], "thenify": ["3.3.1"], "glob": ["10.4.5"], "package-json-from-dist": ["1.0.1"], "foreground-child": ["3.3.1"], "jackspeak": ["3.4.3"], "path-scurry": ["1.11.1"], "minimatch": ["9.0.5"], "cross-spawn": ["7.0.6"], "@isaacs/cliui": ["8.0.2"], "@rollup/rollup-linux-x64-gnu": ["4.45.1"], "@pkgjs/parseargs": ["0.11.0"], "@babel/helper-string-parser": ["7.27.1"], "signal-exit": ["4.1.0"], "brace-expansion": ["2.0.2"], "shebang-command": ["2.0.0"], "path-key": ["3.1.1"], "which": ["2.0.2"], "wrap-ansi": ["7.0.0", "8.1.0"], "string-width": ["5.1.2", "4.2.3"], "strip-ansi": ["7.1.0", "6.0.1"], "balanced-match": ["1.0.2"], "shebang-regex": ["3.0.0"], "eastasianwidth": ["0.2.0"], "isexe": ["2.0.0"], "is-fullwidth-code-point": ["3.0.0"], "ansi-styles": ["4.3.0", "6.2.1"], "ansi-regex": ["5.0.1", "6.1.0"], "emoji-regex": ["8.0.0", "9.2.2"], "color-convert": ["2.0.1"], "color-name": ["1.1.4"], "minipass": ["7.1.2"], "typescript": ["5.6.3"], "@esbuild/linux-x64": ["0.25.6"], "@types/babel__generator": ["7.27.0"], "@types/babel__traverse": ["7.20.7"], "@types/babel__template": ["7.4.4"], "@babel/helper-plugin-utils": ["7.27.1"], "@tauri-apps/cli-linux-x64-gnu": ["2.6.2"]}