export interface ToolInfo {
  name: string;
  description: string;
  parameters: any;
  enabled: boolean;
}

export interface ToolResult {
  tool_name: string;
  success: boolean;
  result: string;
  execution_time: number;
  error?: string;
}

export interface ToolCall {
  id: string;
  name: string;
  parameters: any;
  result?: ToolResult;
}

export interface SecuritySettings {
  tool_execution_enabled: boolean;
  command_whitelist: string[];
  command_blacklist: string[];
  max_execution_time: number;
  sandbox_mode: boolean;
}
