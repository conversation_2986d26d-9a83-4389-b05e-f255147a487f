import { useState, useCallback } from 'react';
import { useChatStore } from '../stores/chatStore';
import { useSettingsStore } from '../stores/settingsStore';
import { chatApi, handleApiError } from '../utils/api';
import { generateId } from '../utils/helpers';
import { ChatMessage } from '../types/chat';

export const useChat = () => {
  const {
    messages,
    currentSession,
    isLoading,
    error,
    addMessage,
    updateMessage,
    deleteMessage,
    clearMessages,
    setLoading,
    setError,
    createNewSession,
    setCurrentSession
  } = useChatStore();

  const { settings } = useSettingsStore();
  const [inputValue, setInputValue] = useState('');

  // 清除错误
  const clearError = useCallback(() => {
    setError(null);
  }, [setError]);

  // 发送消息
  const sendMessage = useCallback(async (content: string) => {
    if (!content.trim() || isLoading) return;

    try {
      setLoading(true);
      setError(null);

      // 如果没有当前会话，创建新会话
      if (!currentSession) {
        const newSession = createNewSession();
        setCurrentSession(newSession);
      }

      // 创建用户消息
      const userMessage: ChatMessage = {
        id: generateId(),
        role: 'user',
        content: content.trim(),
        timestamp: new Date(),
        model_used: undefined,
        tool_calls: undefined,
        rendered_content: undefined
      };

      // 添加用户消息到 store
      addMessage(userMessage);

      // 调用 API 发送消息
      const response = await chatApi.sendMessage(
        content.trim(),
        settings.ai_settings.default_model
      );

      // 创建 AI 响应消息
      const assistantMessage: ChatMessage = {
        id: generateId(),
        role: 'assistant',
        content: response.message,
        timestamp: new Date(),
        model_used: settings.ai_settings.default_model,
        tool_calls: response.tool_results?.map(result => ({
          id: generateId(),
          tool_name: result.tool_name,
          parameters: {},
          result
        })),
        rendered_content: undefined
      };

      // 添加 AI 响应到 store
      addMessage(assistantMessage);

    } catch (error) {
      const errorMessage = handleApiError(error);
      setError(errorMessage);
      console.error('Failed to send message:', error);
    } finally {
      setLoading(false);
    }
  }, [
    isLoading,
    currentSession,
    settings.ai_settings.default_model,
    addMessage,
    setLoading,
    setError,
    createNewSession,
    setCurrentSession
  ]);

  // 清空聊天历史
  const clearChat = useCallback(async () => {
    try {
      await chatApi.clearChatHistory();
      clearMessages();
    } catch (error) {
      const errorMessage = handleApiError(error);
      setError(errorMessage);
      console.error('Failed to clear chat:', error);
    }
  }, [clearMessages, setError]);

  // 新建对话
  const startNewChat = useCallback(() => {
    const newSession = createNewSession();
    setCurrentSession(newSession);
    clearMessages();
  }, [createNewSession, setCurrentSession, clearMessages]);

  // 编辑消息
  const editMessage = useCallback(async (messageId: string, newContent: string) => {
    try {
      // 更新消息内容
      updateMessage(messageId, { content: newContent });

      // 如果编辑的是用户消息，需要重新生成 AI 响应
      const message = messages.find(m => m.id === messageId);
      if (message && message.role === 'user') {
        // 删除该消息之后的所有消息
        const messageIndex = messages.findIndex(m => m.id === messageId);
        const messagesToDelete = messages.slice(messageIndex + 1);
        messagesToDelete.forEach(msg => deleteMessage(msg.id));

        // 重新发送消息
        await sendMessage(newContent);
      }
    } catch (error) {
      const errorMessage = handleApiError(error);
      setError(errorMessage);
      console.error('Failed to edit message:', error);
    }
  }, [messages, updateMessage, deleteMessage, sendMessage, setError]);

  // 删除消息
  const removeMessage = useCallback((messageId: string) => {
    deleteMessage(messageId);
  }, [deleteMessage]);

  // 重新生成响应
  const regenerateResponse = useCallback(async () => {
    if (messages.length === 0 || isLoading) return;

    // 找到最后一条用户消息
    const lastUserMessage = [...messages].reverse().find(msg => msg.role === 'user');
    if (!lastUserMessage) return;

    // 删除最后一条 AI 响应（如果存在）
    const lastMessage = messages[messages.length - 1];
    if (lastMessage && lastMessage.role === 'assistant') {
      deleteMessage(lastMessage.id);
    }

    // 重新发送最后一条用户消息
    await sendMessage(lastUserMessage.content);
  }, [messages, isLoading, sendMessage, deleteMessage]);



  // 处理输入框回车事件
  const handleKeyPress = useCallback((event: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (event.key === 'Enter') {
      if (event.shiftKey) {
        // Shift+Enter 换行，不做任何处理
        return;
      } else if (event.ctrlKey || event.metaKey) {
        // Ctrl+Enter 或 Cmd+Enter 发送消息
        event.preventDefault();
        if (inputValue.trim()) {
          sendMessage(inputValue);
          setInputValue('');
        }
      }
    }
  }, [inputValue, sendMessage]);

  // 处理发送按钮点击
  const handleSend = useCallback(() => {
    if (inputValue.trim()) {
      sendMessage(inputValue);
      setInputValue('');
    }
  }, [inputValue, sendMessage]);

  return {
    // 状态
    messages,
    currentSession,
    isLoading,
    error,
    inputValue,

    // 操作
    sendMessage,
    clearChat,
    startNewChat,
    editMessage,
    removeMessage,
    regenerateResponse,
    clearError,
    setInputValue,
    handleKeyPress,
    handleSend,

    // 设置
    maxTokens: settings.ai_settings.max_tokens,
    currentModel: settings.ai_settings.default_model,
  };
};
