import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import MessageRenderer from '../components/Chat/MessageRenderer/MessageRenderer';
// import Button from '../components/Common/Button';

const DemoPage: React.FC = () => {
  const navigate = useNavigate();
  const [selectedDemo, setSelectedDemo] = useState('markdown');

  const demos = {
    markdown: {
      title: 'Markdown 渲染',
      content: `# Markdown 渲染演示

这是一个 **Markdown** 渲染演示页面，展示了 Sparkle AI 的富文本渲染能力。

## 基本格式

### 文本样式
- **粗体文本**
- *斜体文本*
- ~~删除线文本~~
- \`行内代码\`

### 列表
1. 有序列表项 1
2. 有序列表项 2
   - 嵌套无序列表
   - 另一个嵌套项

### 链接和图片
[访问 GitHub](https://github.com)

### 引用
> 这是一个引用块
> 可以包含多行内容

### 代码块
\`\`\`javascript
function hello() {
    console.log("Hello, Sparkle AI!");
    return "Welcome to the demo!";
}
\`\`\`

### 表格
| 功能 | 状态 | 描述 |
|------|------|------|
| Markdown | ✅ | 完整支持 |
| Mermaid | ✅ | 图表渲染 |
| ECharts | ✅ | 数据可视化 |

### 数学公式
行内公式：$E = mc^2$

块级公式：
$$\\int_{-\\infty}^{\\infty} e^{-x^2} dx = \\sqrt{\\pi}$$

---

这就是 Markdown 渲染的基本功能展示！`
    },
    mermaid: {
      title: 'Mermaid 图表',
      content: `# Mermaid 图表演示

Sparkle AI 支持多种 Mermaid 图表类型：

## 流程图
\`\`\`mermaid
graph TD
    A[开始] --> B{是否登录?}
    B -->|是| C[显示主界面]
    B -->|否| D[显示登录页面]
    D --> E[用户输入凭据]
    E --> F{验证成功?}
    F -->|是| C
    F -->|否| G[显示错误信息]
    G --> D
    C --> H[结束]
\`\`\`

## 序列图
\`\`\`mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant B as 后端
    participant D as 数据库
    
    U->>F: 发送消息
    F->>B: API 请求
    B->>D: 查询数据
    D-->>B: 返回结果
    B-->>F: 响应数据
    F-->>U: 显示结果
\`\`\`

## 甘特图
\`\`\`mermaid
gantt
    title Sparkle AI 开发计划
    dateFormat  YYYY-MM-DD
    section 核心功能
    聊天界面        :done,    des1, 2024-01-01,2024-01-15
    AI 集成         :done,    des2, 2024-01-10,2024-01-25
    工具系统        :active,  des3, 2024-01-20,2024-02-05
    section 扩展功能
    思源笔记集成    :         des4, 2024-01-25,2024-02-10
    富文本渲染      :         des5, 2024-02-01,2024-02-15
    安全系统        :         des6, 2024-02-10,2024-02-25
\`\`\`

这些图表都是实时渲染的，支持交互和导出功能！`
    },
    echarts: {
      title: 'ECharts 数据可视化',
      content: `# ECharts 数据可视化演示

Sparkle AI 集成了 ECharts，支持丰富的数据可视化：

## 折线图
\`\`\`echarts
{
  "title": {
    "text": "用户增长趋势",
    "left": "center"
  },
  "tooltip": {
    "trigger": "axis"
  },
  "legend": {
    "data": ["新用户", "活跃用户"],
    "top": "10%"
  },
  "xAxis": {
    "type": "category",
    "data": ["1月", "2月", "3月", "4月", "5月", "6月"]
  },
  "yAxis": {
    "type": "value"
  },
  "series": [
    {
      "name": "新用户",
      "type": "line",
      "data": [120, 132, 101, 134, 90, 230],
      "smooth": true
    },
    {
      "name": "活跃用户",
      "type": "line",
      "data": [220, 182, 191, 234, 290, 330],
      "smooth": true
    }
  ]
}
\`\`\`

## 饼图
\`\`\`echarts
{
  "title": {
    "text": "功能使用分布",
    "left": "center"
  },
  "tooltip": {
    "trigger": "item"
  },
  "legend": {
    "orient": "vertical",
    "left": "left"
  },
  "series": [
    {
      "name": "使用量",
      "type": "pie",
      "radius": "50%",
      "data": [
        {"value": 1048, "name": "聊天对话"},
        {"value": 735, "name": "工具执行"},
        {"value": 580, "name": "笔记同步"},
        {"value": 484, "name": "文件操作"},
        {"value": 300, "name": "其他功能"}
      ],
      "emphasis": {
        "itemStyle": {
          "shadowBlur": 10,
          "shadowOffsetX": 0,
          "shadowColor": "rgba(0, 0, 0, 0.5)"
        }
      }
    }
  ]
}
\`\`\`

## 柱状图
\`\`\`echarts
{
  "title": {
    "text": "工具使用统计",
    "left": "center"
  },
  "tooltip": {
    "trigger": "axis",
    "axisPointer": {
      "type": "shadow"
    }
  },
  "xAxis": {
    "type": "category",
    "data": ["Shell", "搜索", "地图", "笔记", "文件"]
  },
  "yAxis": {
    "type": "value"
  },
  "series": [
    {
      "name": "使用次数",
      "type": "bar",
      "data": [320, 280, 150, 200, 100],
      "itemStyle": {
        "color": "#5470c6"
      }
    }
  ]
}
\`\`\`

所有图表都支持交互、缩放、导出等功能！`
    },
    mixed: {
      title: '混合内容',
      content: `# 混合内容演示

这个演示展示了如何在同一个消息中混合使用 Markdown、Mermaid 和 ECharts：

## 项目架构说明

Sparkle AI 采用现代化的技术栈构建：

### 系统架构图
\`\`\`mermaid
graph TB
    subgraph "前端层"
        A[React + TypeScript]
        B[Tailwind CSS]
        C[Tauri WebView]
    end
    
    subgraph "后端层"
        D[Rust + Tauri]
        E[SQLite 数据库]
        F[工具系统]
    end
    
    subgraph "外部服务"
        G[AI API]
        H[思源笔记]
        I[网络服务]
    end
    
    A --> D
    B --> C
    C --> D
    D --> E
    D --> F
    F --> G
    F --> H
    F --> I
\`\`\`

### 性能指标

以下是系统的关键性能指标：

\`\`\`echarts
{
  "title": {
    "text": "系统性能监控",
    "left": "center"
  },
  "tooltip": {
    "trigger": "axis"
  },
  "legend": {
    "data": ["响应时间", "内存使用", "CPU 使用率"]
  },
  "xAxis": {
    "type": "category",
    "data": ["00:00", "04:00", "08:00", "12:00", "16:00", "20:00"]
  },
  "yAxis": [
    {
      "type": "value",
      "name": "时间(ms)/使用率(%)",
      "position": "left"
    }
  ],
  "series": [
    {
      "name": "响应时间",
      "type": "line",
      "data": [120, 110, 150, 180, 160, 140],
      "yAxisIndex": 0
    },
    {
      "name": "内存使用",
      "type": "line",
      "data": [45, 48, 52, 58, 55, 50],
      "yAxisIndex": 0
    },
    {
      "name": "CPU 使用率",
      "type": "line",
      "data": [15, 12, 25, 35, 28, 20],
      "yAxisIndex": 0
    }
  ]
}
\`\`\`

### 功能特性

- ✅ **多模型支持**：集成多个 AI 模型
- ✅ **工具生态**：丰富的工具集成
- ✅ **安全机制**：完善的权限控制
- ✅ **富文本渲染**：支持 Markdown、图表等
- ✅ **本地存储**：SQLite 数据库
- ✅ **跨平台**：基于 Tauri 的桌面应用

### 代码示例

\`\`\`rust
// Rust 后端示例
use tauri::State;
use crate::types::*;

#[tauri::command]
pub async fn execute_tool(
    tool_name: String,
    params: serde_json::Value,
    state: State<'_, AppState>,
) -> AppResult<ToolResult> {
    // 安全验证
    let security_manager = SecurityManager::new();
    security_manager.validate_tool_execution(&tool_name, &params).await?;
    
    // 执行工具
    let tool_manager = ToolManager::new();
    tool_manager.execute_tool(&tool_name, params).await
}
\`\`\`

这就是 Sparkle AI 的完整功能展示！`
    }
  };

  const demoOptions = [
    { id: 'markdown', label: 'Markdown 渲染', icon: '📝' },
    { id: 'mermaid', label: 'Mermaid 图表', icon: '📊' },
    { id: 'echarts', label: 'ECharts 可视化', icon: '📈' },
    { id: 'mixed', label: '混合内容', icon: '🎨' },
  ];

  return (
    <div className="flex h-screen bg-gray-50 dark:bg-gray-900">
      {/* 侧边栏 */}
      <div className="w-64 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700">
        <div className="p-4">
          <div className="flex items-center space-x-3">
            <button
              onClick={() => navigate('/chat')}
              className="p-1 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              title="返回聊天"
            >
              <span className="text-gray-500 dark:text-gray-400">←</span>
            </button>
            <h1 className="text-xl font-bold text-gray-900 dark:text-white">功能演示</h1>
          </div>
        </div>
        
        <nav className="px-4 space-y-1">
          {demoOptions.map((option) => (
            <button
              key={option.id}
              onClick={() => setSelectedDemo(option.id)}
              className={`w-full text-left p-3 rounded-lg transition-colors ${
                selectedDemo === option.id
                  ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300'
                  : 'hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300'
              }`}
            >
              <span className="mr-3">{option.icon}</span>
              {option.label}
            </button>
          ))}
        </nav>
      </div>

      {/* 主内容区 */}
      <div className="flex-1 overflow-y-auto">
        <div className="p-6">
          <div className="max-w-4xl mx-auto">
            <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
              <div className="p-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900">
                <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                  {demos[selectedDemo as keyof typeof demos].title}
                </h2>
              </div>
              <div className="p-6">
                <MessageRenderer 
                  content={demos[selectedDemo as keyof typeof demos].content}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DemoPage;
