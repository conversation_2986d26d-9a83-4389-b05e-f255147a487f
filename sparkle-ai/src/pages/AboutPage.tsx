import React from 'react';
import { useNavigate } from 'react-router-dom';
import Button from '../components/Common/Button';

const AboutPage: React.FC = () => {
  const navigate = useNavigate();

  const features = [
    {
      icon: '🤖',
      title: 'AI 对话',
      description: '支持多种 AI 模型，包括 DeepSeek、通义千问等，提供智能对话体验'
    },
    {
      icon: '🧠',
      title: '记忆管理',
      description: '智能记忆系统，AI 可以记住重要信息，提供个性化服务'
    },
    {
      icon: '🛠️',
      title: '工具集成',
      description: '集成多种实用工具，包括文件操作、网络搜索、地图查询等'
    },
    {
      icon: '📝',
      title: '思源笔记',
      description: '与思源笔记深度集成，支持笔记创建、搜索和管理'
    },
    {
      icon: '⚙️',
      title: '个性化设置',
      description: '丰富的设置选项，支持主题切换、快捷键配置等'
    },
    {
      icon: '🔒',
      title: '安全可靠',
      description: '本地运行，数据安全，支持命令白名单和沙箱模式'
    }
  ];

  const techStack = [
    { name: 'Tau<PERSON>', description: '跨平台桌面应用框架' },
    { name: 'React', description: '现代化前端框架' },
    { name: 'TypeScript', description: '类型安全的 JavaScript' },
    { name: 'Tailwind CSS', description: '实用优先的 CSS 框架' },
    { name: 'Zustand', description: '轻量级状态管理' },
    { name: 'Rust', description: '高性能后端语言' },
    { name: 'SQLite', description: '轻量级数据库' }
  ];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* 头部 */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                onClick={() => navigate('/chat')}
                className="p-2"
              >
                ← 返回
              </Button>
              <h1 className="text-xl font-bold text-gray-900 dark:text-white">
                关于 Sparkle-AI
              </h1>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 项目介绍 */}
        <div className="text-center mb-12">
          <div className="text-6xl mb-4">✨</div>
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
            Sparkle-AI
          </h2>
          <p className="text-lg text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
            一个现代化的智能系统助手，基于 Tauri 构建的跨平台桌面应用。
            集成多种 AI 模型，提供智能对话、记忆管理、工具集成等功能，
            让您的工作更加高效便捷。
          </p>
        </div>

        {/* 功能特性 */}
        <div className="mb-12">
          <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            核心功能
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {features.map((feature, index) => (
              <div
                key={index}
                className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-shadow"
              >
                <div className="text-3xl mb-4">{feature.icon}</div>
                <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  {feature.title}
                </h4>
                <p className="text-gray-600 dark:text-gray-400 text-sm">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>

        {/* 技术栈 */}
        <div className="mb-12">
          <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            技术栈
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {techStack.map((tech, index) => (
              <div
                key={index}
                className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700 text-center"
              >
                <h4 className="font-semibold text-gray-900 dark:text-white mb-1">
                  {tech.name}
                </h4>
                <p className="text-xs text-gray-600 dark:text-gray-400">
                  {tech.description}
                </p>
              </div>
            ))}
          </div>
        </div>

        {/* 开发状态 */}
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
          <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-4">
            开发状态
          </h3>
          <div className="space-y-3">
            <div className="flex items-center">
              <span className="text-green-500 mr-2">✅</span>
              <span className="text-gray-700 dark:text-gray-300">基础框架搭建完成</span>
            </div>
            <div className="flex items-center">
              <span className="text-green-500 mr-2">✅</span>
              <span className="text-gray-700 dark:text-gray-300">UI 组件库开发完成</span>
            </div>
            <div className="flex items-center">
              <span className="text-green-500 mr-2">✅</span>
              <span className="text-gray-700 dark:text-gray-300">状态管理系统完成</span>
            </div>
            <div className="flex items-center">
              <span className="text-green-500 mr-2">✅</span>
              <span className="text-gray-700 dark:text-gray-300">模拟 API 系统完成</span>
            </div>
            <div className="flex items-center">
              <span className="text-yellow-500 mr-2">🚧</span>
              <span className="text-gray-700 dark:text-gray-300">后端 API 集成开发中</span>
            </div>
            <div className="flex items-center">
              <span className="text-gray-400 mr-2">⏳</span>
              <span className="text-gray-500 dark:text-gray-400">AI 模型集成待开发</span>
            </div>
            <div className="flex items-center">
              <span className="text-gray-400 mr-2">⏳</span>
              <span className="text-gray-500 dark:text-gray-400">工具系统待开发</span>
            </div>
          </div>
        </div>

        {/* 底部按钮 */}
        <div className="text-center mt-8">
          <Button
            onClick={() => navigate('/chat')}
            size="lg"
          >
            开始体验
          </Button>
        </div>
      </div>
    </div>
  );
};

export default AboutPage;
