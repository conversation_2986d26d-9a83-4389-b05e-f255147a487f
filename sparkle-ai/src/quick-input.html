<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快速输入</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 8px;
            overflow: hidden;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 8px 16px;
            font-size: 12px;
            font-weight: 500;
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: move;
        }

        .close-btn {
            background: none;
            border: none;
            color: white;
            cursor: pointer;
            font-size: 16px;
            padding: 2px 6px;
            border-radius: 4px;
            transition: background-color 0.2s;
        }

        .close-btn:hover {
            background-color: rgba(255, 255, 255, 0.2);
        }

        .content {
            flex: 1;
            padding: 16px;
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .input-area {
            flex: 1;
            border: 2px solid #e1e5e9;
            border-radius: 6px;
            padding: 12px;
            font-size: 14px;
            font-family: inherit;
            resize: none;
            outline: none;
            transition: border-color 0.2s;
        }

        .input-area:focus {
            border-color: #667eea;
        }

        .actions {
            display: flex;
            gap: 8px;
            justify-content: flex-end;
        }

        .btn {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a6fd8;
        }

        .btn-secondary {
            background: #f8f9fa;
            color: #6c757d;
            border: 1px solid #dee2e6;
        }

        .btn-secondary:hover {
            background: #e9ecef;
        }

        .status {
            font-size: 11px;
            color: #6c757d;
            text-align: center;
            padding: 4px;
        }

        .loading {
            color: #667eea;
        }

        .error {
            color: #dc3545;
        }

        .success {
            color: #28a745;
        }
    </style>
</head>
<body>
    <div class="header" data-tauri-drag-region>
        <span>快速输入 - Sparkle AI</span>
        <button class="close-btn" onclick="closeWindow()">×</button>
    </div>
    
    <div class="content">
        <textarea 
            class="input-area" 
            placeholder="输入您的问题或指令..."
            id="inputText"
            autofocus
        ></textarea>
        
        <div class="actions">
            <button class="btn btn-secondary" onclick="clearInput()">清空</button>
            <button class="btn btn-primary" onclick="sendMessage()" id="sendBtn">发送</button>
        </div>
        
        <div class="status" id="status"></div>
    </div>

    <script>
        const { invoke } = window.__TAURI__.tauri;
        const { appWindow } = window.__TAURI__.window;

        let isProcessing = false;

        // 自动聚焦输入框
        document.getElementById('inputText').focus();

        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                closeWindow();
            } else if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                e.preventDefault();
                sendMessage();
            }
        });

        async function sendMessage() {
            if (isProcessing) return;

            const inputText = document.getElementById('inputText');
            const sendBtn = document.getElementById('sendBtn');
            const status = document.getElementById('status');
            
            const message = inputText.value.trim();
            if (!message) {
                showStatus('请输入内容', 'error');
                return;
            }

            try {
                isProcessing = true;
                sendBtn.disabled = true;
                sendBtn.textContent = '发送中...';
                showStatus('正在处理...', 'loading');

                // 发送消息到主窗口
                await invoke('send_message', { message });
                
                showStatus('消息已发送', 'success');
                inputText.value = '';
                
                // 延迟关闭窗口
                setTimeout(() => {
                    closeWindow();
                }, 1000);

            } catch (error) {
                console.error('发送消息失败:', error);
                showStatus('发送失败: ' + error, 'error');
            } finally {
                isProcessing = false;
                sendBtn.disabled = false;
                sendBtn.textContent = '发送';
            }
        }

        function clearInput() {
            document.getElementById('inputText').value = '';
            document.getElementById('inputText').focus();
            showStatus('');
        }

        function closeWindow() {
            appWindow.hide();
        }

        function showStatus(message, type = '') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = 'status ' + type;
        }

        // 窗口显示时自动聚焦
        appWindow.onFocusChanged(({ payload: focused }) => {
            if (focused) {
                setTimeout(() => {
                    document.getElementById('inputText').focus();
                }, 100);
            }
        });

        // 监听主窗口的响应
        appWindow.listen('quick-input-response', (event) => {
            const { success, message } = event.payload;
            if (success) {
                showStatus('处理完成', 'success');
            } else {
                showStatus('处理失败: ' + message, 'error');
            }
        });
    </script>
</body>
</html>
