import React from 'react';
import <PERSON><PERSON><PERSON>enderer from './MarkdownRenderer';
import MermaidRenderer, { extractMermaidBlocks } from './MermaidRenderer';
import EChartsRenderer, { extractEChartsBlocks } from './EChartsRenderer';

interface MessageRendererProps {
  content: string;
  className?: string;
}

interface ContentBlock {
  type: 'markdown' | 'mermaid' | 'echarts';
  content: string;
  id?: string;
  config?: any;
}

const MessageRenderer: React.FC<MessageRendererProps> = ({ content, className = '' }) => {
  // 解析内容，提取不同类型的块
  const parseContent = (text: string): ContentBlock[] => {
    const blocks: ContentBlock[] = [];
    let remainingContent = text;

    // 提取 Mermaid 块
    const mermaidBlocks = extractMermaidBlocks(text);
    mermaidBlocks.forEach(block => {
      const blockPattern = new RegExp(`\`\`\`mermaid\\n${block.definition.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\n\`\`\``, 'g');
      remainingContent = remainingContent.replace(blockPattern, `__MERMAID_PLACEHOLDER_${block.id}__`);
    });

    // 提取 ECharts 块
    const echartsBlocks = extractEChartsBlocks(text);
    echartsBlocks.forEach(block => {
      const configString = typeof block.config === 'string' ? block.config : JSON.stringify(block.config, null, 2);
      const blockPattern = new RegExp(`\`\`\`echarts\\n${configString.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\n\`\`\``, 'g');
      remainingContent = remainingContent.replace(blockPattern, `__ECHARTS_PLACEHOLDER_${block.id}__`);
    });

    // 分割剩余内容
    const parts = remainingContent.split(/(__(?:MERMAID|ECHARTS)_PLACEHOLDER_[^_]+__)/);
    
    parts.forEach(part => {
      if (part.startsWith('__MERMAID_PLACEHOLDER_')) {
        const id = part.replace('__MERMAID_PLACEHOLDER_', '').replace('__', '');
        const mermaidBlock = mermaidBlocks.find(block => block.id === id);
        if (mermaidBlock) {
          blocks.push({
            type: 'mermaid',
            content: mermaidBlock.definition,
            id: mermaidBlock.id,
          });
        }
      } else if (part.startsWith('__ECHARTS_PLACEHOLDER_')) {
        const id = part.replace('__ECHARTS_PLACEHOLDER_', '').replace('__', '');
        const echartsBlock = echartsBlocks.find(block => block.id === id);
        if (echartsBlock) {
          blocks.push({
            type: 'echarts',
            content: '',
            id: echartsBlock.id,
            config: echartsBlock.config,
          });
        }
      } else if (part.trim()) {
        blocks.push({
          type: 'markdown',
          content: part.trim(),
        });
      }
    });

    return blocks;
  };

  const contentBlocks = parseContent(content);

  return (
    <div className={`message-renderer ${className}`}>
      {contentBlocks.map((block, index) => {
        const key = block.id || `${block.type}-${index}`;
        
        switch (block.type) {
          case 'mermaid':
            return (
              <div key={key} className="mb-4">
                <MermaidRenderer 
                  definition={block.content}
                  id={block.id}
                />
              </div>
            );
            
          case 'echarts':
            return (
              <div key={key} className="mb-4">
                <EChartsRenderer
                  config={block.config}
                />
              </div>
            );
            
          case 'markdown':
          default:
            return (
              <div key={key}>
                <MarkdownRenderer content={block.content} />
              </div>
            );
        }
      })}
    </div>
  );
};

// 工具函数：检查内容是否包含特殊渲染块
export const hasSpecialBlocks = (content: string): boolean => {
  return content.includes('```mermaid') || content.includes('```echarts');
};

// 工具函数：获取内容中的特殊块统计
export const getBlockStats = (content: string) => {
  const mermaidBlocks = extractMermaidBlocks(content);
  const echartsBlocks = extractEChartsBlocks(content);
  
  return {
    mermaid: mermaidBlocks.length,
    echarts: echartsBlocks.length,
    total: mermaidBlocks.length + echartsBlocks.length,
  };
};

// 工具函数：预处理内容，移除特殊块用于纯文本显示
export const stripSpecialBlocks = (content: string): string => {
  return content
    .replace(/```mermaid\n[\s\S]*?\n```/g, '[Mermaid 图表]')
    .replace(/```echarts\n[\s\S]*?\n```/g, '[ECharts 图表]');
};

export default MessageRenderer;
