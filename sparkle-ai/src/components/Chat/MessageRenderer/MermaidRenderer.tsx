import React, { useRef, useState } from 'react';
// import mermaid from 'mermaid';

interface MermaidRendererProps {
  definition: string;
  id?: string;
  className?: string;
}

const MermaidRenderer: React.FC<MermaidRendererProps> = ({
  definition,
  // id = `mermaid-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
  className = ''
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [error] = useState<string | null>(null);
  const [isLoading] = useState(false);

  const copyToClipboard = () => {
    navigator.clipboard.writeText(definition).then(() => {
      // 可以添加一个临时的成功提示
    });
  };

  const downloadSVG = () => {
    const svgElement = containerRef.current?.querySelector('svg');
    if (svgElement) {
      const svgData = new XMLSerializer().serializeToString(svgElement);
      const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });
      const svgUrl = URL.createObjectURL(svgBlob);
      
      const downloadLink = document.createElement('a');
      downloadLink.href = svgUrl;
      downloadLink.download = `mermaid-diagram-${Date.now()}.svg`;
      document.body.appendChild(downloadLink);
      downloadLink.click();
      document.body.removeChild(downloadLink);
      URL.revokeObjectURL(svgUrl);
    }
  };

  if (error) {
    return (
      <div className={`mermaid-error border border-red-300 rounded-lg p-4 bg-red-50 dark:bg-red-900/20 ${className}`}>
        <div className="flex items-center justify-between mb-2">
          <h4 className="text-red-800 dark:text-red-200 font-semibold">Mermaid 图表渲染错误</h4>
          <button
            onClick={copyToClipboard}
            className="text-xs bg-red-500 hover:bg-red-600 text-white px-2 py-1 rounded"
          >
            复制代码
          </button>
        </div>
        <p className="text-red-600 dark:text-red-300 text-sm mb-3">{error}</p>
        <details className="text-sm">
          <summary className="cursor-pointer text-red-700 dark:text-red-300 hover:text-red-800 dark:hover:text-red-200">
            查看原始代码
          </summary>
          <pre className="mt-2 p-2 bg-red-100 dark:bg-red-800/30 rounded text-red-800 dark:text-red-200 overflow-x-auto">
            {definition}
          </pre>
        </details>
      </div>
    );
  }

  return (
    <div className={`mermaid-container border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden ${className}`}>
      <div className="flex items-center justify-between bg-gray-100 dark:bg-gray-800 px-4 py-2">
        <span className="text-sm text-gray-600 dark:text-gray-400 font-medium">
          📊 Mermaid 图表
        </span>
        <div className="flex space-x-2">
          <button
            onClick={copyToClipboard}
            className="text-xs bg-blue-500 hover:bg-blue-600 text-white px-2 py-1 rounded"
            title="复制代码"
          >
            复制
          </button>
          <button
            onClick={downloadSVG}
            className="text-xs bg-green-500 hover:bg-green-600 text-white px-2 py-1 rounded"
            title="下载 SVG"
            disabled={isLoading || !!error}
          >
            下载
          </button>
        </div>
      </div>
      
      <div className="p-4 bg-white dark:bg-gray-900">
        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <span className="ml-2 text-gray-600 dark:text-gray-400">渲染中...</span>
          </div>
        ) : (
          <div
            ref={containerRef}
            className="mermaid-diagram flex justify-center items-center bg-gray-100 dark:bg-gray-800 rounded border-2 border-dashed border-gray-300 dark:border-gray-600"
            style={{ minHeight: '200px' }}
          >
            <div className="text-center">
              <div className="text-4xl mb-2">📊</div>
              <div className="text-gray-600 dark:text-gray-400">
                Mermaid 图表预览
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-500 mt-2">
                需要安装 mermaid 依赖
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

// 用于检测文本中的 Mermaid 代码块
export const extractMermaidBlocks = (content: string): Array<{ id: string; definition: string; type: string }> => {
  const mermaidRegex = /```mermaid\n([\s\S]*?)\n```/g;
  const blocks: Array<{ id: string; definition: string; type: string }> = [];
  let match;

  while ((match = mermaidRegex.exec(content)) !== null) {
    const definition = match[1].trim();
    const id = `mermaid-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    // 尝试检测图表类型
    let type = 'unknown';
    if (definition.includes('graph') || definition.includes('flowchart')) {
      type = 'flowchart';
    } else if (definition.includes('sequenceDiagram')) {
      type = 'sequence';
    } else if (definition.includes('gantt')) {
      type = 'gantt';
    } else if (definition.includes('pie')) {
      type = 'pie';
    } else if (definition.includes('gitGraph')) {
      type = 'git';
    } else if (definition.includes('classDiagram')) {
      type = 'class';
    } else if (definition.includes('stateDiagram')) {
      type = 'state';
    } else if (definition.includes('erDiagram')) {
      type = 'er';
    }

    blocks.push({ id, definition, type });
  }

  return blocks;
};

export default MermaidRenderer;
